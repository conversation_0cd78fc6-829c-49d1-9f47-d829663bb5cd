<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg  PUBLIC '-//W3C//DTD SVG 1.1//EN'  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
<defs>
<font id="Gotham-Bold" horiz-adv-x="790">
<font-face panose-1="0 0 0 0 0 0 0 0 0 0" underline-position="-150" bbox="-44 -173 1092 928" cap-height="700" descent="-200" unicode-range="U+0020-00FE" x-height="536" underline-thickness="50" font-weight="400" font-family="Gotham Bold" ascent="800" units-per-em="1000"/>
<missing-glyph/>
<glyph horiz-adv-x="300" unicode=" " glyph-name="space"/>
<glyph d="M120 238l-47 442v20h178v-20l-47 -442h-84zM81 0v160h162v-160h-162z" horiz-adv-x="324" unicode="!" glyph-name="exclam"/>
<glyph d="M293 381l18 319h168v-5l-109 -314h-77zM57 381l18 319h168v-5l-109 -314h-77z" horiz-adv-x="520" unicode='&quot;' glyph-name="quotedbl"/>
<glyph d="M630 278v-128h-111l-25 -150h-132l25 150h-144l-26 -150h-132l25 150h-75v128h97l26 148h-88v128h110l25 146h132l-25 -146h145l25 146h132l-25 -146h76v-128h-98l-26 -148h89zM287 430l-26 -156h151l26 156h-151z" horiz-adv-x="700" unicode="#" glyph-name="numbersign"/>
<glyph d="M570 624l-67 -117q-63 43 -124 59v-152q115 -31 166 -81t51 -133v-2q0 -83 -58 -135t-154 -62v-99h-104v101q-133 17 -236 95l78 114q86 -65 163 -81v159q-113 30 -164 78t-51 134v2q0 82 57 134t153 62v58h104v-61q98 -12 186 -73zM450 192q0 25 -15.5 42t-55.5 32v-140 q71 11 71 64v2zM216 511q0 -25 14.5 -41.5t54.5 -30.5v136q-69 -8 -69 -62v-2z" horiz-adv-x="646" unicode="$" glyph-name="dollar"/>
<glyph d="M214 350q-74 0 -121.5 51t-47.5 126v2q0 76 48 127.5t123 51.5q74 0 121.5 -51t47.5 -126v-2q0 -75 -48 -127t-123 -52zM214 617q-30 0 -47 -25t-17 -61v-2q0 -36 18.5 -62t47.5 -26q30 0 47 25t17 61v2q0 36 -18.5 62t-47.5 26zM395 377l231 323h119l-280 -377 l-231 -323h-119zM644 -8q-74 0 -121.5 51t-47.5 126v2q0 76 48 127.5t123 51.5q74 0 121.5 -51t47.5 -126v-2q0 -75 -48 -127t-123 -52zM646 83q30 0 47 25t17 61v2q0 36 -18.5 62t-47.5 26q-30 0 -47 -25t-17 -61v-2q0 -36 18.5 -62t47.5 -26z" horiz-adv-x="860" unicode="%" glyph-name="percent"/>
<glyph d="M555 -14l-81 82q-95 -78 -205 -78q-102 0 -168.5 55.5t-66.5 146.5v2q0 130 136 192q-49 69 -49 140v2q0 76 57.5 130t154.5 54q88 0 142.5 -50.5t54.5 -125.5v-2q0 -126 -147 -183l93 -93q33 52 68 120l113 -62q-52 -94 -92 -148l103 -103zM303 432q48 18 70.5 39.5 t22.5 53.5v2q0 30 -18 47.5t-48 17.5q-31 0 -49.5 -19.5t-18.5 -51.5v-2q0 -40 41 -87zM287 116q50 0 101 40l-134 136q-65 -34 -65 -92v-2q0 -37 27.5 -59.5t70.5 -22.5z" horiz-adv-x="696" unicode="&amp;" glyph-name="ampersand"/>
<glyph d="M57 381l18 319h170v-5l-109 -314h-79z" horiz-adv-x="286" unicode="&apos;" glyph-name="quotesingle"/>
<glyph d="M336 -141q-139 76 -208 180.5t-69 247.5t69 247.5t208 180.5l67 -101q-102 -71 -147.5 -146.5t-45.5 -180.5t45.5 -180.5t147.5 -146.5z" horiz-adv-x="445" unicode="(" glyph-name="parenleft"/>
<glyph d="M109 -141l-67 101q102 71 147.5 146.5t45.5 180.5t-45.5 180.5t-147.5 146.5l67 101q139 -76 208 -180.5t69 -247.5t-69 -247.5t-208 -180.5z" horiz-adv-x="445" unicode=")" glyph-name="parenright"/>
<glyph d="M174 372l16 120l-97 -74l-42 72l112 47l-112 47l42 72l97 -74l-16 120h82l-16 -120l97 74l42 -72l-112 -47l112 -47l-42 -72l-97 74l16 -120h-82z" horiz-adv-x="430" unicode="*" glyph-name="asterisk"/>
<glyph d="M249 103v180h-181v138h181v180h142v-180h181v-138h-181v-180h-142z" horiz-adv-x="640" unicode="+" glyph-name="plus"/>
<glyph d="M39 -162l-13 63q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" horiz-adv-x="286" unicode="," glyph-name="comma"/>
<glyph d="M50 237v146h308v-146h-308z" horiz-adv-x="408" unicode="-" glyph-name="hyphen"/>
<glyph d="M62 0v160h162v-160h-162z" horiz-adv-x="286" unicode="." glyph-name="period"/>
<glyph d="M-28 -128l443 926h129l-443 -926h-129z" horiz-adv-x="530" unicode="/" glyph-name="slash"/>
<glyph d="M364 -12q-138 0 -224 102.5t-86 257.5v2q0 155 87 258.5t225 103.5q137 0 223.5 -103t86.5 -257v-2q0 -155 -87 -258.5t-225 -103.5zM366 127q69 0 110.5 62t41.5 159v2q0 96 -43 159.5t-111 63.5t-110 -62t-42 -159v-2q0 -97 42.5 -160t111.5 -63z" horiz-adv-x="730" unicode="0" glyph-name="zero"/>
<glyph d="M178 0v549l-122 -30l-32 126l201 60h105v-705h-152z" horiz-adv-x="422" unicode="1" glyph-name="one"/>
<glyph d="M49 0v122l233 191q69 57 95 91.5t26 76.5q0 43 -26 67t-68 24q-39 0 -70.5 -21.5t-72.5 -72.5l-108 87q55 75 113.5 110t147.5 35q109 0 175 -59t66 -157v-2q0 -57 -22 -102.5t-57 -80t-105 -88.5l-114 -88h305v-133h-518z" horiz-adv-x="623" unicode="2" glyph-name="two"/>
<glyph d="M311 -12q-171 0 -273 121l106 101q75 -84 169 -84q44 0 71.5 23t27.5 62v2q0 43 -36 66t-101 23h-64l-24 98l167 168h-275v132h473v-116l-177 -169q188 -32 188 -196v-2q0 -101 -68.5 -165t-183.5 -64z" horiz-adv-x="621" unicode="3" glyph-name="three"/>
<glyph d="M398 0v151h-342l-25 109l384 445h130v-429h94v-125h-94v-151h-147zM212 276h186v218z" horiz-adv-x="689" unicode="4" glyph-name="four"/>
<glyph d="M305 -12q-152 0 -262 105l93 111q84 -76 167 -76q55 0 86 25.5t31 71.5v2q0 44 -34 69.5t-90 25.5t-117 -26l-89 59l20 345h430v-135h-298l-8 -121q48 12 92 12q109 0 177 -55.5t68 -168.5v-2q0 -111 -72.5 -176.5t-193.5 -65.5z" horiz-adv-x="630" unicode="5" glyph-name="five"/>
<glyph d="M339 -12q-127 0 -202 75q-83 83 -83 265v2q0 170 78 276t229 106q122 0 219 -77l-81 -119q-38 29 -69.5 42.5t-73.5 13.5q-37 0 -65 -16t-43.5 -42.5t-23 -52t-9.5 -51.5q67 45 145 45q108 0 177 -58.5t69 -164.5v-2q0 -106 -76 -174t-191 -68zM333 120q56 0 88 28.5 t32 74.5v2q0 45 -33 73.5t-89 28.5t-88 -28t-32 -73v-2q0 -45 33 -74.5t89 -29.5z" horiz-adv-x="662" unicode="6" glyph-name="six"/>
<glyph d="M84 0l313 567h-329v133h504v-116l-314 -584h-174z" horiz-adv-x="619" unicode="7" glyph-name="seven"/>
<glyph d="M315 -10q-118 0 -194 56t-76 146v2q0 117 113 169q-89 54 -89 153v2q0 82 68.5 137t177.5 55t177.5 -55t68.5 -137v-2q0 -99 -89 -153q57 -30 85 -68t28 -97v-2q0 -95 -75.5 -150.5t-194.5 -55.5zM315 414q45 0 72.5 24t27.5 61v2q0 34 -27.5 58t-72.5 24 q-46 0 -73 -23.5t-27 -57.5v-2q0 -37 27.5 -61.5t72.5 -24.5zM315 117q56 0 88.5 24.5t32.5 62.5v2q0 40 -33.5 63t-87.5 23t-87.5 -23t-33.5 -63v-2q0 -37 33 -62t88 -25z" horiz-adv-x="630" unicode="8" glyph-name="eight"/>
<glyph d="M306 128q37 0 65.5 16t43.5 42t22.5 50.5t10.5 50.5q-58 -48 -142 -48q-115 0 -182.5 60t-67.5 166v2q0 106 74.5 175.5t192.5 69.5q69 0 115.5 -18t86.5 -58q83 -83 83 -264v-2q0 -174 -80 -278t-226 -104q-130 0 -230 83l81 117q75 -60 153 -60zM331 367q56 0 88 29 t32 75v2q0 47 -33 77t-90 30q-56 0 -87.5 -29t-31.5 -77v-2q0 -47 33 -76t89 -29z" horiz-adv-x="662" unicode="9" glyph-name="nine"/>
<glyph d="M67 376v160h162v-160h-162zM67 0v160h162v-160h-162z" horiz-adv-x="296" unicode=":" glyph-name="colon"/>
<glyph d="M67 376v160h162v-160h-162zM44 -162l-13 63q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" horiz-adv-x="296" unicode=";" glyph-name="semicolon"/>
<glyph d="M552 71l-484 212v138l484 212v-141l-338 -139l338 -139v-143z" horiz-adv-x="640" unicode="&lt;" glyph-name="less"/>
<glyph d="M73 413v138h494v-138h-494zM73 153v138h494v-138h-494z" horiz-adv-x="640" unicode="=" glyph-name="equal"/>
<glyph d="M88 71v141l338 139l-338 139v143l484 -212v-138z" horiz-adv-x="640" unicode="&gt;" glyph-name="greater"/>
<glyph d="M197 238l-23 162l5 5q175 7 175 91v2q0 34 -24 52.5t-68 18.5q-81 0 -150 -69l-92 101q98 108 245 108q110 0 176 -54.5t66 -152.5v-2q0 -161 -197 -201l-11 -61h-102zM164 0v160h162v-160h-162z" horiz-adv-x="544" unicode="?" glyph-name="question"/>
<glyph d="M499 -162q-190 0 -318 127.5t-128 308.5q0 180 127.5 309t310.5 129q180 0 308 -119.5t128 -276.5q0 -120 -56 -185.5t-143 -65.5q-101 0 -153 60q-66 -60 -148 -60q-78 0 -130.5 53t-52.5 140q0 104 67.5 177.5t156.5 73.5q88 0 138 -68l11 53l118 -19l-47 -269 q-3 -15 -3 -31q0 -28 19.5 -45.5t53.5 -17.5q55 0 91 54t36 150q0 142 -114.5 249t-279.5 107q-168 0 -282 -116t-114 -282q0 -167 114.5 -281.5t289.5 -114.5q131 0 245 67l20 -32q-123 -75 -265 -75zM462 168q43 0 76 33q43 43 43 111q0 42 -26 68t-67 26q-44 0 -78 -34 q-42 -42 -42 -107q0 -46 26 -71.5t68 -25.5z" horiz-adv-x="980" unicode="@" glyph-name="at"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227z" unicode="A" glyph-name="A"/>
<glyph d="M84 0v700h325q121 0 183 -62q48 -48 48 -119v-2q0 -99 -92 -151q65 -25 98 -65t33 -107v-2q0 -94 -70 -143t-192 -49h-333zM487 491v2q0 35 -26 53.5t-75 18.5h-152v-148h142q111 0 111 74zM526 213q0 76 -114 76h-178v-154h183q109 0 109 76v2z" horiz-adv-x="722" unicode="B" glyph-name="B"/>
<glyph d="M412 -12q-154 0 -256.5 104t-102.5 256v2q0 152 103 257t262 105q93 0 158 -27.5t118 -80.5l-98 -113q-87 79 -179 79q-87 0 -145 -63.5t-58 -154.5v-2q0 -92 57.5 -156t145.5 -64q56 0 97 20t87 62l98 -99q-58 -62 -124.5 -93.5t-162.5 -31.5z" horiz-adv-x="738" unicode="C" glyph-name="C"/>
<glyph d="M84 0v700h273q163 0 267.5 -99.5t104.5 -248.5v-2q0 -150 -104.5 -250t-267.5 -100h-273zM357 561h-119v-422h119q94 0 152.5 58t58.5 151v2q0 93 -58.5 152t-152.5 59z" horiz-adv-x="782" unicode="D" glyph-name="D"/>
<glyph d="M84 0v700h528v-137h-375v-142h330v-137h-330v-147h380v-137h-533z" horiz-adv-x="670" unicode="E" glyph-name="E"/>
<glyph d="M84 0v700h533v-140h-379v-149h334v-140h-334v-271h-154z" horiz-adv-x="656" unicode="F" glyph-name="F"/>
<glyph d="M421 -12q-162 0 -265 102t-103 258v2q0 151 105 256.5t262 105.5q91 0 154 -23.5t121 -72.5l-97 -117q-44 37 -85 54t-98 17q-84 0 -142.5 -64t-58.5 -154v-2q0 -96 59.5 -159t152.5 -63q86 0 145 42v100h-155v133h304v-304q-131 -111 -299 -111z" horiz-adv-x="784" unicode="G" glyph-name="G"/>
<glyph d="M84 0v700h154v-277h284v277h154v-700h-154v281h-284v-281h-154z" horiz-adv-x="760" unicode="H" glyph-name="H"/>
<glyph d="M91 0v700h154v-700h-154z" horiz-adv-x="336" unicode="I" glyph-name="I"/>
<glyph d="M243 -10q-141 0 -229 105l97 108q64 -70 128 -70q94 0 94 116v451h158v-458q0 -121 -66 -187q-65 -65 -182 -65z" horiz-adv-x="564" unicode="J" glyph-name="J"/>
<glyph d="M84 0v700h154v-306l284 306h186l-285 -297l298 -403h-185l-217 298l-81 -84v-214h-154z" horiz-adv-x="730" unicode="K" glyph-name="K"/>
<glyph d="M84 0v700h154v-560h349v-140h-503z" horiz-adv-x="619" unicode="L" glyph-name="L"/>
<glyph d="M84 0v700h166l184 -296l184 296h166v-700h-153v457l-197 -299h-4l-195 296v-454h-151z" horiz-adv-x="868" unicode="M" glyph-name="M"/>
<glyph d="M84 0v700h142l328 -431v431h152v-700h-131l-339 445v-445h-152z" unicode="N" glyph-name="N"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64z" horiz-adv-x="850" unicode="O" glyph-name="O"/>
<glyph d="M84 0v700h286q124 0 196 -66.5t72 -175.5v-2q0 -117 -80 -181.5t-203 -64.5h-117v-210h-154zM238 347h122q56 0 89 30t33 76v2q0 51 -33 78.5t-92 27.5h-119v-214z" horiz-adv-x="668" unicode="P" glyph-name="P"/>
<glyph d="M807 88l-98 -109l-77 69q-93 -60 -208 -60q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -109 -62 -201zM426 130q53 0 92 19l-114 97l98 110l115 -104q19 44 19 96v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2 q0 -92 60 -156t152 -64z" horiz-adv-x="850" unicode="Q" glyph-name="Q"/>
<glyph d="M84 0v700h320q133 0 204 -71q60 -60 60 -160v-2q0 -161 -150 -217l171 -250h-180l-150 224h-121v-224h-154zM238 360h156q55 0 86.5 27t31.5 72v2q0 49 -32 74.5t-89 25.5h-153v-201z" horiz-adv-x="723" unicode="R" glyph-name="R"/>
<glyph d="M333 -10q-172 0 -297 112l91 109q103 -85 209 -85q47 0 74 17.5t27 48.5v2q0 30 -28 48.5t-113 40.5q-60 15 -97 29.5t-70.5 38.5t-49 59.5t-15.5 86.5v2q0 95 68.5 153t176.5 58q149 0 260 -89l-80 -116q-99 69 -182 69q-43 0 -66.5 -17t-23.5 -44v-2q0 -34 29 -51 t122 -41q114 -30 168 -76.5t54 -133.5v-2q0 -102 -70.5 -159.5t-186.5 -57.5z" horiz-adv-x="640" unicode="S" glyph-name="S"/>
<glyph d="M247 0v558h-213v142h580v-142h-213v-558h-154z" horiz-adv-x="648" unicode="T" glyph-name="T"/>
<glyph d="M376 -11q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395q0 -156 -81.5 -236t-225.5 -80z" horiz-adv-x="756" unicode="U" glyph-name="U"/>
<glyph d="M307 -5l-283 705h170l183 -493l183 493h166l-283 -705h-136z" horiz-adv-x="750" unicode="V" glyph-name="V"/>
<glyph d="M269 -5l-239 705h165l145 -474l157 476h132l157 -476l145 474h161l-239 -705h-134l-158 458l-158 -458h-134z" horiz-adv-x="1122" unicode="W" glyph-name="W"/>
<glyph d="M516 700h175l-234 -342l244 -358h-180l-157 240l-158 -240h-175l244 356l-234 344h180l146 -227z" horiz-adv-x="732" unicode="X" glyph-name="X"/>
<glyph d="M281 0v276l-269 424h180l167 -281l170 281h175l-269 -421v-279h-154z" horiz-adv-x="716" unicode="Y" glyph-name="Y"/>
<glyph d="M63 0v117l385 448h-373v135h571v-117l-385 -448h385v-135h-583z" horiz-adv-x="708" unicode="Z" glyph-name="Z"/>
<glyph d="M83 -130v830h323v-119h-177v-592h177v-119h-323z" horiz-adv-x="465" unicode="[" glyph-name="bracketleft"/>
<glyph d="M429 -128l-443 926h129l443 -926h-129z" horiz-adv-x="530" unicode="\" glyph-name="backslash"/>
<glyph d="M59 -130v119h177v592h-177v119h323v-830h-323z" horiz-adv-x="465" unicode="]" glyph-name="bracketright"/>
<glyph d="M50 493l148 207h104l148 -207h-110l-91 117l-91 -117h-108z" horiz-adv-x="500" unicode="^" glyph-name="asciicircum"/>
<glyph d="M-2 -160v116h604v-116h-604z" horiz-adv-x="600" unicode="_" glyph-name="underscore"/>
<glyph d="M240 595l-130 120l131 57l111 -177h-112z" horiz-adv-x="500" unicode="`" glyph-name="grave"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5z" horiz-adv-x="592" unicode="a" glyph-name="a"/>
<glyph d="M387 -10q-100 0 -167 80v-70h-152v730h152v-271q64 87 167 87q100 0 172 -74.5t72 -202.5v-2q0 -128 -71.5 -202.5t-172.5 -74.5zM348 119q55 0 93 41t38 107v2q0 65 -38 106.5t-93 41.5t-92.5 -41.5t-37.5 -106.5v-2q0 -65 37.5 -106.5t92.5 -41.5z" horiz-adv-x="675" unicode="b" glyph-name="b"/>
<glyph d="M321 -12q-120 0 -199.5 80.5t-79.5 196.5v2q0 116 80 197.5t201 81.5q134 0 214 -90l-93 -100q-56 59 -122 59q-56 0 -93 -43t-37 -103v-2q0 -63 37.5 -105.5t98.5 -42.5q63 0 123 57l89 -90q-44 -48 -94 -73t-125 -25z" horiz-adv-x="571" unicode="c" glyph-name="c"/>
<glyph d="M288 -10q-100 0 -172 74.5t-72 202.5v2q0 128 71.5 202.5t172.5 74.5q100 0 167 -80v264h152v-730h-152v77q-64 -87 -167 -87zM327 119q55 0 92.5 41.5t37.5 106.5v2q0 65 -37.5 106.5t-92.5 41.5t-93 -41t-38 -107v-2q0 -65 38 -106.5t93 -41.5z" horiz-adv-x="675" unicode="d" glyph-name="d"/>
<glyph d="M322 -12q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100zM191 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5z" horiz-adv-x="613" unicode="e" glyph-name="e"/>
<glyph d="M94 0v406h-63v125h63v34q0 87 43 130q42 42 123 42q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21h123v-124h-121v-406h-152z" horiz-adv-x="382" unicode="f" glyph-name="f"/>
<glyph d="M308 -162q-131 0 -237 56l52 114q87 -49 181 -49q153 0 153 146v26q-36 -44 -76.5 -65.5t-98.5 -21.5q-99 0 -168.5 67.5t-69.5 182.5v2q0 115 70 182.5t168 67.5q100 0 173 -80v70h152v-415q0 -143 -68 -211q-72 -72 -231 -72zM326 170q56 0 93.5 35t37.5 89v2 q0 54 -37.5 89t-93.5 35t-93 -35t-37 -89v-2q0 -55 37 -89.5t93 -34.5z" horiz-adv-x="675" unicode="g" glyph-name="g"/>
<glyph d="M68 0v730h152v-270q67 86 157 86q86 0 134 -53t48 -146v-347h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152z" horiz-adv-x="623" unicode="h" glyph-name="h"/>
<glyph d="M71 595v135h160v-135h-160zM75 0v536h152v-536h-152z" horiz-adv-x="302" unicode="i" glyph-name="i"/>
<glyph d="M71 595v135h160v-135h-160zM55 -163q-34 0 -66 6v119q18 -3 32 -3q54 0 54 62v515h152v-527q0 -172 -172 -172z" horiz-adv-x="302" unicode="j" glyph-name="j"/>
<glyph d="M68 0v730h152v-389l178 195h182l-204 -211l211 -325h-176l-137 220l-54 -57v-163h-152z" horiz-adv-x="592" unicode="k" glyph-name="k"/>
<glyph d="M75 0v730h152v-730h-152z" horiz-adv-x="302" unicode="l" glyph-name="l"/>
<glyph d="M68 0v536h152v-76q67 86 158 86q107 0 151 -85q73 85 176 85q86 0 133.5 -50.5t47.5 -146.5v-349h-152v299q0 109 -89 109q-43 0 -67.5 -28t-24.5 -81v-299h-152v299q0 109 -89 109q-43 0 -67.5 -28t-24.5 -81v-299h-152z" horiz-adv-x="950" unicode="m" glyph-name="m"/>
<glyph d="M68 0v536h152v-76q67 86 157 86q86 0 134 -53t48 -146v-347h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152z" horiz-adv-x="623" unicode="n" glyph-name="n"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5z" horiz-adv-x="664" unicode="o" glyph-name="o"/>
<glyph d="M68 -160v696h152v-77q64 87 167 87q100 0 172 -74.5t72 -202.5v-2q0 -128 -71.5 -202.5t-172.5 -74.5q-100 0 -167 80v-230h-152zM348 119q55 0 93 41t38 107v2q0 65 -38 106.5t-93 41.5t-92.5 -41.5t-37.5 -106.5v-2q0 -65 37.5 -106.5t92.5 -41.5z" horiz-adv-x="675" unicode="p" glyph-name="p"/>
<glyph d="M455 -160v237q-64 -87 -167 -87q-100 0 -172 74.5t-72 202.5v2q0 128 71.5 202.5t172.5 74.5q100 0 167 -80v70h152v-696h-152zM327 119q55 0 92.5 41.5t37.5 106.5v2q0 65 -37.5 106.5t-92.5 41.5t-93 -41t-38 -107v-2q0 -65 38 -106.5t93 -41.5z" horiz-adv-x="675" unicode="q" glyph-name="q"/>
<glyph d="M68 0v536h152v-108q52 123 171 118v-159h-8q-78 0 -120.5 -47.5t-42.5 -141.5v-198h-152z" horiz-adv-x="418" unicode="r" glyph-name="r"/>
<glyph d="M256 -10q-127 0 -230 81l65 100q88 -64 169 -64q64 0 64 40v2q0 5 -1.5 10t-5.5 9t-8.5 7.5t-12 7t-13 6t-15.5 6t-17 5.5t-19 5.5t-19 5.5q-34 10 -58 20.5t-49.5 28.5t-39 46.5t-13.5 65.5v2q0 79 56 124.5t140 45.5q108 0 200 -62l-58 -105q-85 50 -145 50 q-27 0 -41.5 -10.5t-14.5 -26.5v-2q0 -27 92 -58q11 -4 17 -6q35 -12 58.5 -22.5t50 -29t40 -45.5t13.5 -62v-2q0 -86 -56.5 -129.5t-148.5 -43.5z" horiz-adv-x="502" unicode="s" glyph-name="s"/>
<glyph d="M249 -9q-75 0 -115.5 36.5t-40.5 124.5v254h-64v130h64v137h152v-137h126v-130h-126v-229q0 -52 49 -52q40 0 75 19v-122q-52 -31 -120 -31z" horiz-adv-x="415" unicode="t" glyph-name="t"/>
<glyph d="M246 -10q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536h-152v76q-67 -86 -157 -86z" horiz-adv-x="623" unicode="u" glyph-name="u"/>
<glyph d="M231 -4l-211 540h161l120 -359l121 359h158l-211 -540h-138z" horiz-adv-x="600" unicode="v" glyph-name="v"/>
<glyph d="M189 -4l-165 540h154l86 -325l101 327h131l102 -328l88 326h151l-167 -540h-137l-103 329l-105 -329h-136z" horiz-adv-x="861" unicode="w" glyph-name="w"/>
<glyph d="M193 536l100 -157l101 157h159l-181 -262l189 -274h-163l-108 167l-108 -167h-159l188 272l-181 264h163z" horiz-adv-x="584" unicode="x" glyph-name="x"/>
<glyph d="M303 171l117 365h158l-206 -549q-31 -83 -70 -116.5t-106 -33.5q-72 0 -135 37l51 110q35 -21 65 -21q36 0 53 35l-210 538h161z" horiz-adv-x="600" unicode="y" glyph-name="y"/>
<glyph d="M50 0v107l277 306h-268v123h457v-107l-278 -306h278v-123h-466z" horiz-adv-x="563" unicode="z" glyph-name="z"/>
<glyph d="M416 -141q-132 23 -183 70t-51 128q0 7 1.5 33.5t1.5 35.5q0 58 -25 80.5t-85 22.5h-26v116h26q60 0 85 22.5t25 80.5q0 9 -1.5 35.5t-1.5 33.5q0 81 51 128t183 70l27 -101q-76 -22 -99 -46.5t-23 -72.5q0 -10 1.5 -36.5t1.5 -37.5q0 -100 -104 -134q104 -34 104 -134 q0 -11 -1.5 -37.5t-1.5 -36.5q0 -48 23 -72.5t99 -46.5z" horiz-adv-x="490" unicode="{" glyph-name="braceleft"/>
<glyph d="M115 -128v926h120v-926h-120z" horiz-adv-x="350" unicode="|" glyph-name="bar"/>
<glyph d="M74 -141l-27 101q76 22 99 46.5t23 72.5q0 10 -1.5 36.5t-1.5 37.5q0 100 104 134q-104 34 -104 134q0 11 1.5 37.5t1.5 36.5q0 48 -23 72.5t-99 46.5l27 101q132 -23 183 -70t51 -128q0 -7 -1.5 -33.5t-1.5 -35.5q0 -58 25 -80.5t85 -22.5h26v-116h-26q-60 0 -85 -22.5 t-25 -80.5q0 -9 1.5 -35.5t1.5 -33.5q0 -81 -51 -128t-183 -70z" horiz-adv-x="490" unicode="}" glyph-name="braceright"/>
<glyph d="M337 234q-36 0 -91 22q-43 17 -59 17q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q36 0 91 -22q43 -17 59 -17q22 0 33.5 11.5t23.5 37.5l81 -25q-19 -69 -47 -103.5t-77 -34.5z" horiz-adv-x="510" unicode="~" glyph-name="asciitilde"/>
<glyph horiz-adv-x="300" unicode=" " glyph-name="uni00A0"/>
<glyph d="M81 540v160h162v-160h-162zM73 0v20l47 442h84l47 -442v-20h-178z" horiz-adv-x="324" unicode="¡" glyph-name="exclamdown"/>
<glyph d="M451 706l-18 -88q64 -22 109 -74l-90 -97q-24 23 -49 37l-54 -276q56 8 107 56l86 -87q-88 -98 -214 -101l-16 -82h-104l19 98q-80 30 -130 101t-50 160v2q0 117 80 198t200 81h6l14 72h104zM194 355q0 -91 64 -130l54 277q-51 -4 -84.5 -45.5t-33.5 -99.5v-2z" horiz-adv-x="581" unicode="¢" glyph-name="cent"/>
<glyph d="M60 0v101l76 25v141h-76v127h76v81q0 111 60 171q64 64 177 64q140 0 225 -115l-117 -94q-27 35 -51 50.5t-55 15.5q-35 0 -57 -22q-26 -26 -26 -74v-77h228v-127h-228v-134h304v-133h-536z" horiz-adv-x="644" unicode="£" glyph-name="sterling"/>
<glyph d="M85 262v108h143l-214 330h177l153 -259l156 259h172l-215 -330h144v-108h-184v-62h184v-108h-184v-92h-148v92h-184v108h184v62h-184z" horiz-adv-x="686" unicode="¥" glyph-name="yen"/>
<glyph d="M488 238q42 -33 42 -91v-2q0 -71 -54.5 -113t-150.5 -42q-146 0 -233 95l82 82q72 -66 147 -66q66 0 66 36v2q0 18 -22.5 29.5t-87.5 25.5q-120 27 -171 60.5t-51 88.5v2q0 84 89 117q-42 33 -42 91v2q0 72 54.5 113.5t150.5 41.5q146 0 233 -95l-82 -82q-72 66 -147 66 q-66 0 -66 -36v-2q0 -17 21 -28.5t89 -26.5q121 -27 171.5 -59.5t50.5 -89.5v-2q0 -84 -89 -117zM389 289q24 0 39.5 12t15.5 32v2q0 19 -23.5 32t-77.5 26q-72 18 -100 18q-24 0 -39.5 -12t-15.5 -32v-2q0 -19 23.5 -32t77.5 -26q72 -18 100 -18z" horiz-adv-x="632" unicode="§" glyph-name="section"/>
<glyph d="M281 595v135h146v-135h-146zM73 595v135h146v-135h-146z" horiz-adv-x="500" unicode="¨" glyph-name="dieresis"/>
<glyph d="M414 -12q-151 0 -256 105.5t-105 254.5v2q0 149 106 255.5t257 106.5t256 -105.5t105 -254.5v-2q0 -149 -106 -255.5t-257 -106.5zM414 28q136 0 228.5 94t92.5 228v2q0 134 -91.5 227t-227.5 93t-228.5 -94t-92.5 -228v-2q0 -134 91.5 -227t227.5 -93zM419 156 q-84 0 -140.5 57t-56.5 140v2q0 83 56.5 141t143.5 58q91 0 152 -59l-55 -64q-48 43 -97 43q-47 0 -78.5 -34.5t-31.5 -83.5v-2q0 -49 31 -83.5t79 -34.5q49 0 99 45l56 -56q-64 -69 -158 -69z" horiz-adv-x="830" unicode="©" glyph-name="copyright"/>
<glyph d="M153 399q-44 0 -72.5 24t-28.5 66v2q0 46 32 70t87 24q36 0 72 -12v5q0 54 -64 54q-38 0 -81 -16l-21 63q53 24 114 24q67 0 100 -33q32 -32 32 -94v-171h-81v32q-33 -38 -89 -38zM49 277v66h278v-66h-278zM178 457q29 0 47.5 14.5t18.5 37.5v15q-27 11 -54 11 q-57 0 -57 -40v-2q0 -16 12.5 -26t32.5 -10z" horiz-adv-x="390" unicode="ª" glyph-name="ordfeminine"/>
<glyph d="M491 42l-178 222v8l178 222l106 -52l-120 -173l120 -173zM219 42l-178 222v8l178 222l106 -52l-120 -173l120 -173z" horiz-adv-x="645" unicode="«" glyph-name="guillemotleft"/>
<glyph d="M184 360q-73 0 -123.5 51t-50.5 123v1q0 72 51 123.5t123 51.5q73 0 123.5 -51t50.5 -123v-1q0 -72 -51 -123.5t-123 -51.5zM184 379q65 0 109.5 46t44.5 110v1q0 64 -44.5 109.5t-109.5 45.5t-109.5 -46t-44.5 -110v-1q0 -64 44.5 -109.5t109.5 -45.5zM108 446v184h88 q77 0 77 -62q0 -43 -38 -58l44 -64h-60l-37 55h-22v-55h-52zM160 542h33q28 0 28 22t-28 22h-33v-44z" horiz-adv-x="368" unicode="®" glyph-name="registered"/>
<glyph d="M70 600v105h360v-105h-360z" horiz-adv-x="500" unicode="¯" glyph-name="macron"/>
<glyph d="M240 368q-69 0 -121 50t-52 118v2q0 68 52 118t121 50t121 -50t52 -118v-2q0 -68 -52 -118t-121 -50zM240 443q39 0 65.5 27.5t26.5 65.5v2q0 38 -26.5 65.5t-65.5 27.5t-65.5 -27.5t-26.5 -65.5v-2q0 -38 26.5 -65.5t65.5 -27.5z" horiz-adv-x="480" unicode="°" glyph-name="degree"/>
<glyph d="M567 379l-180 1l1 -177h-136l1 177l-180 -1v132l180 -1l-1 177h136l-1 -177l180 1v-132zM567 0h-494v126h494v-126z" horiz-adv-x="640" unicode="±" glyph-name="plusminus"/>
<glyph d="M52 420v75l133 108q38 30 52.5 49t14.5 41q0 21 -15 33.5t-38 12.5q-43 0 -85 -53l-62 51q32 42 66 62.5t86 20.5q63 0 101 -33t38 -86q0 -40 -18.5 -69t-63.5 -65l-90 -71h174v-76h-293z" horiz-adv-x="400" unicode="²" glyph-name="two.sup"/>
<glyph d="M196 410q-87 0 -156 60l51 65q53 -46 106 -46q24 0 41.5 13.5t17.5 34.5q0 48 -72 48h-47l-14 55l104 92h-160v78h270v-68l-111 -94q53 -4 85.5 -31.5t32.5 -76.5q0 -58 -40 -94t-108 -36z" horiz-adv-x="400" unicode="³" glyph-name="three.sup"/>
<glyph d="M148 595l111 177l131 -57l-130 -120h-112z" horiz-adv-x="500" unicode="´" glyph-name="acute"/>
<glyph d="M352 0v230h-24q-131 0 -212 60.5t-81 172.5v2q0 109 75.5 172t200.5 63h195v-700h-154z" horiz-adv-x="590" unicode="¶" glyph-name="paragraph"/>
<glyph d="M62 230v160h162v-160h-162z" horiz-adv-x="286" unicode="·" glyph-name="periodcentered"/>
<glyph d="M245 -173l-127 53l96 140h109z" horiz-adv-x="500" unicode="¸" glyph-name="cedilla"/>
<glyph d="M230 420h-86v303l-75 -28l-25 72l122 48h64v-395z" horiz-adv-x="325" unicode="¹" glyph-name="one.sup"/>
<glyph d="M194 398q-66 0 -109.5 44t-43.5 108v2q0 64 44.5 108.5t110.5 44.5t109.5 -44t43.5 -108v-2q0 -64 -44.5 -108.5t-110.5 -44.5zM51 277v66h288v-66h-288zM196 470q32 0 51.5 23.5t19.5 56.5v2q0 33 -20.5 57t-52.5 24t-51.5 -23.5t-19.5 -56.5v-2q0 -33 20.5 -57 t52.5 -24z" horiz-adv-x="390" unicode="º" glyph-name="ordmasculine"/>
<glyph d="M426 42l-106 52l120 173l-120 173l106 54l178 -222v-8zM154 42l-106 52l120 173l-120 173l106 54l178 -222v-8z" horiz-adv-x="645" unicode="»" glyph-name="guillemotright"/>
<glyph d="M739 80v-82h-80v82h-188l-8 83l188 231h88v-245h52v-69h-52zM121 0l254 367l221 333h83l-254 -367l-221 -333h-83zM230 310h-86v303l-75 -28l-25 72l122 48h64v-395zM660 148v152l-122 -152h122z" horiz-adv-x="825" unicode="¼" glyph-name="onequarter"/>
<glyph d="M230 310h-86v303l-75 -28l-25 72l122 48h64v-395zM487 0v75l133 108q38 30 52.5 49t14.5 41q0 21 -15 33.5t-38 12.5q-43 0 -85 -53l-62 51q32 42 66 62.5t86 20.5q63 0 101 -33t38 -86q0 -40 -18.5 -69t-63.5 -65l-90 -71h174v-76h-293zM111 0l254 367l221 333h83 l-254 -367l-221 -333h-83z" horiz-adv-x="825" unicode="½" glyph-name="onehalf"/>
<glyph d="M814 80v-82h-80v82h-188l-8 83l188 231h88v-245h52v-69h-52zM196 300q-87 0 -156 60l51 65q53 -46 106 -46q24 0 41.5 13.5t17.5 34.5q0 48 -72 48h-47l-14 55l104 92h-160v78h270v-68l-111 -94q53 -4 85.5 -31.5t32.5 -76.5q0 -58 -40 -94t-108 -36zM191 0l254 367 l221 333h83l-254 -367l-221 -333h-83zM735 148v152l-122 -152h122z" horiz-adv-x="900" unicode="¾" glyph-name="threequarters"/>
<glyph d="M218 540v160h162v-160h-162zM279 -9q-110 0 -176 54.5t-66 152.5v2q0 161 197 201l11 61h102l23 -162l-5 -5q-175 -7 -175 -91v-2q0 -34 24 -52.5t68 -18.5q81 0 150 69l92 -101q-98 -108 -245 -108z" horiz-adv-x="544" unicode="¿" glyph-name="questiondown"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM457 765h-116l-136 100l131 57z" unicode="À" glyph-name="Agrave"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM333 765l121 157l131 -57l-136 -100h-116z" unicode="Á" glyph-name="Aacute"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM195 760l128 147h144l128 -147h-122l-79 56l-79 -56h-120z" unicode="Â" glyph-name="Acircumflex"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM485 765q-36 0 -97 23q-43 16 -59 16q-22 0 -34.5 -11.5t-23.5 -37.5l-81 25q19 69 47.5 103.5t77.5 34.5q36 0 97 -23q43 -16 59 -16q22 0 34.5 11.5t23.5 37.5l81 -25 q-19 -69 -47.5 -103.5t-77.5 -34.5z" unicode="Ã" glyph-name="Atilde"/>
<glyph d="M24 0l300 705h142l300 -705h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM431 765v127h146v-127h-146zM213 765v127h146v-127h-146z" unicode="Ä" glyph-name="Adieresis"/>
<glyph d="M24 0l282 664q-35 30 -35 78t36.5 79t87.5 31t87.5 -31t36.5 -79q0 -46 -35 -78l282 -664h-161l-64 157h-296l-64 -157h-157zM300 293h186l-93 227zM395 686q28 0 45.5 16t17.5 40t-17.5 40t-45.5 16t-45.5 -16t-17.5 -40t17.5 -40t45.5 -16z" unicode="Å" glyph-name="Aring"/>
<glyph d="M9 0l377 700h601v-137h-350v-142h305v-137h-305v-147h355v-137h-508v157h-235l-83 -157h-157zM321 293h163v274h-18z" horiz-adv-x="1045" unicode="Æ" glyph-name="AE"/>
<glyph d="M373 -173l-127 53l80 118q-121 27 -197 124t-76 226v2q0 152 103 257t262 105q93 0 158 -27.5t118 -80.5l-98 -113q-87 79 -179 79q-87 0 -145 -63.5t-58 -154.5v-2q0 -92 57.5 -156t145.5 -64q56 0 97 20t87 62l98 -99q-54 -58 -115 -89t-146 -35z" horiz-adv-x="738" unicode="Ç" glyph-name="Ccedilla"/>
<glyph d="M84 0v700h528v-137h-375v-142h330v-137h-330v-147h380v-137h-533zM412 755h-116l-136 100l131 57z" horiz-adv-x="670" unicode="È" glyph-name="Egrave"/>
<glyph d="M84 0v700h528v-137h-375v-142h330v-137h-330v-147h380v-137h-533zM288 755l121 157l131 -57l-136 -100h-116z" horiz-adv-x="670" unicode="É" glyph-name="Eacute"/>
<glyph d="M84 0v700h528v-137h-375v-142h330v-137h-330v-147h380v-137h-533zM150 755l128 147h144l128 -147h-122l-79 56l-79 -56h-120z" horiz-adv-x="670" unicode="Ê" glyph-name="Ecircumflex"/>
<glyph d="M84 0v700h528v-137h-375v-142h330v-137h-330v-147h380v-137h-533zM386 755v127h146v-127h-146zM168 755v127h146v-127h-146z" horiz-adv-x="670" unicode="Ë" glyph-name="Edieresis"/>
<glyph d="M91 0v700h154v-700h-154zM230 755h-116l-136 100l131 57z" horiz-adv-x="336" unicode="Ì" glyph-name="Igrave"/>
<glyph d="M91 0v700h154v-700h-154zM106 755l121 157l131 -57l-136 -100h-116z" horiz-adv-x="336" unicode="Í" glyph-name="Iacute"/>
<glyph d="M91 0v700h154v-700h-154zM-32 755l128 147h144l128 -147h-122l-79 56l-79 -56h-120z" horiz-adv-x="336" unicode="Î" glyph-name="Icircumflex"/>
<glyph d="M91 0v700h154v-700h-154zM204 755v127h146v-127h-146zM-14 755v127h146v-127h-146z" horiz-adv-x="336" unicode="Ï" glyph-name="Idieresis"/>
<glyph d="M114 0v284h-75v136h75v280h273q163 0 267.5 -99.5t104.5 -248.5v-2q0 -150 -104.5 -250t-267.5 -100h-273zM418 284h-150v-145h119q94 0 152.5 58t58.5 151v2q0 93 -58.5 152t-152.5 59h-119v-141h150v-136z" horiz-adv-x="812" unicode="Ð" glyph-name="Eth"/>
<glyph d="M84 0v700h142l328 -431v431h152v-700h-131l-339 445v-445h-152zM485 755q-36 0 -97 23q-43 16 -59 16q-22 0 -34.5 -11.5t-23.5 -37.5l-81 25q19 69 47.5 103.5t77.5 34.5q36 0 97 -23q43 -16 59 -16q22 0 34.5 11.5t23.5 37.5l81 -25q-19 -69 -47.5 -103.5t-77.5 -34.5z " unicode="Ñ" glyph-name="Ntilde"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64zM487 765h-116 l-136 100l131 57z" horiz-adv-x="850" unicode="Ò" glyph-name="Ograve"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64zM363 765l121 157 l131 -57l-136 -100h-116z" horiz-adv-x="850" unicode="Ó" glyph-name="Oacute"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64zM225 765l128 147 h144l128 -147h-122l-79 56l-79 -56h-120z" horiz-adv-x="850" unicode="Ô" glyph-name="Ocircumflex"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64zM515 765 q-36 0 -97 23q-43 16 -59 16q-22 0 -34.5 -11.5t-23.5 -37.5l-81 25q19 69 47.5 103.5t77.5 34.5q36 0 97 -23q43 -16 59 -16q22 0 34.5 11.5t23.5 37.5l81 -25q-19 -69 -47.5 -103.5t-77.5 -34.5z" horiz-adv-x="850" unicode="Õ" glyph-name="Otilde"/>
<glyph d="M424 -12q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -151 -106.5 -256.5t-266.5 -105.5zM426 130q92 0 151 63t59 155v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64zM461 765v127h146 v-127h-146zM243 765v127h146v-127h-146z" horiz-adv-x="850" unicode="Ö" glyph-name="Odieresis"/>
<glyph d="M474 103l-155 159l-155 -159l-93 93l159 155l-159 155l95 95l155 -159l155 159l93 -93l-159 -155l159 -155z" horiz-adv-x="640" unicode="×" glyph-name="multiply"/>
<glyph d="M658 705h147l-98 -114q90 -99 90 -239v-2q0 -151 -106.5 -256.5t-266.5 -105.5q-107 0 -193 50l-39 -43h-147l98 114q-90 99 -90 239v2q0 151 106.5 256.5t266.5 105.5q104 0 193 -50zM426 127q92 0 152.5 64.5t60.5 156.5v2q0 67 -35 124l-277 -323q46 -24 99 -24z M211 350q0 -69 35 -124l277 323q-46 24 -99 24q-92 0 -152.5 -64.5t-60.5 -156.5v-2z" horiz-adv-x="850" unicode="Ø" glyph-name="Oslash"/>
<glyph d="M376 -11q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395q0 -156 -81.5 -236t-225.5 -80zM440 755h-116l-136 100l131 57z" horiz-adv-x="756" unicode="Ù" glyph-name="Ugrave"/>
<glyph d="M376 -11q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395q0 -156 -81.5 -236t-225.5 -80zM316 755l121 157l131 -57l-136 -100h-116z" horiz-adv-x="756" unicode="Ú" glyph-name="Uacute"/>
<glyph d="M376 -11q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395q0 -156 -81.5 -236t-225.5 -80zM178 755l128 147h144l128 -147h-122l-79 56l-79 -56h-120z" horiz-adv-x="756" unicode="Û" glyph-name="Ucircumflex"/>
<glyph d="M376 -11q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395q0 -156 -81.5 -236t-225.5 -80zM414 755v127h146v-127h-146zM196 755v127h146v-127h-146z" horiz-adv-x="756" unicode="Ü" glyph-name="Udieresis"/>
<glyph d="M281 0v276l-269 424h180l167 -281l170 281h175l-269 -421v-279h-154zM296 755l121 157l131 -57l-136 -100h-116z" horiz-adv-x="716" unicode="Ý" glyph-name="Yacute"/>
<glyph d="M84 0v700h154v-102h132q124 0 196 -66.5t72 -175.5v-2q0 -117 -80 -181.5t-203 -64.5h-117v-108h-154zM238 245h122q56 0 89 30t33 76v2q0 51 -33 78.5t-92 27.5h-119v-214z" horiz-adv-x="673" unicode="Þ" glyph-name="Thorn"/>
<glyph d="M299 -2v114q128 6 128 92v2q0 39 -35.5 64t-92.5 33v100q88 56 88 124v2q0 37 -21.5 58.5t-58.5 21.5q-39 0 -63.5 -27.5t-24.5 -72.5v-509h-151v503q0 110 66 172t180 62q104 0 163.5 -54.5t59.5 -139.5v-2q0 -59 -24 -96t-67 -67q131 -57 131 -177v-2q0 -99 -75 -152.5 t-203 -48.5z" horiz-adv-x="620" unicode="ß" glyph-name="germandbls"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM246 595l-130 120l131 57l111 -177h-112z" horiz-adv-x="592" unicode="à" glyph-name="agrave"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM238 595l111 177l131 -57l-130 -120h-112z" horiz-adv-x="592" unicode="á" glyph-name="aacute"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM103 595l123 157h144l123 -157h-117l-79 66l-79 -66h-115z" horiz-adv-x="592" unicode="â" glyph-name="acircumflex"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM385 595q-36 0 -91 22q-43 17 -59 17q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q36 0 91 -22q43 -17 59 -17q22 0 33.5 11.5t23.5 37.5l81 -25q-19 -69 -47 -103.5t-77 -34.5z" horiz-adv-x="592" unicode="ã" glyph-name="atilde"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM329 595v135h146v-135h-146zM121 595v135h146v-135h-146z" horiz-adv-x="592" unicode="ä" glyph-name="adieresis"/>
<glyph d="M220 -10q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q121 0 181 -60q58 -58 58 -171v-311h-147v58q-61 -68 -162 -68zM266 95q53 0 86 26.5t33 67.5v27q-43 20 -97 20q-49 0 -77 -19 t-28 -55v-2q0 -30 23 -47.5t60 -17.5zM298 593q-50 0 -85.5 34t-35.5 83t35.5 83t85.5 34t85.5 -34t35.5 -83t-35.5 -83t-85.5 -34zM298 644q27 0 46.5 19.5t19.5 46.5t-19.5 46.5t-46.5 19.5t-46.5 -19.5t-19.5 -46.5t19.5 -46.5t46.5 -19.5z" horiz-adv-x="592" unicode="å" glyph-name="aring"/>
<glyph d="M238 -10q-89 0 -145.5 44t-56.5 120v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q126 0 182 -68q70 72 177 72q83 0 143 -42t87 -106t27 -143q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77 q-81 -100 -219 -100q-125 0 -204 81q-107 -79 -218 -79zM529 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM266 97q71 0 137 51q-15 35 -19 69q-45 19 -96 19q-49 0 -77 -19t-28 -55v-2q0 -29 24 -46t59 -17z" horiz-adv-x="951" unicode="æ" glyph-name="ae"/>
<glyph d="M281 -173l-127 53l82 120q-87 26 -140.5 98.5t-53.5 166.5v2q0 116 80 197.5t201 81.5q134 0 214 -90l-93 -100q-56 59 -122 59q-56 0 -93 -43t-37 -103v-2q0 -63 37.5 -105.5t98.5 -42.5q63 0 123 57l89 -90q-81 -90 -194 -97z" horiz-adv-x="571" unicode="ç" glyph-name="ccedilla"/>
<glyph d="M322 -12q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100zM191 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM253 595l-130 120l131 57 l111 -177h-112z" horiz-adv-x="613" unicode="è" glyph-name="egrave"/>
<glyph d="M322 -12q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100zM191 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM245 595l111 177l131 -57 l-130 -120h-112z" horiz-adv-x="613" unicode="é" glyph-name="eacute"/>
<glyph d="M322 -12q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100zM191 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM110 595l123 157h144 l123 -157h-117l-79 66l-79 -66h-115z" horiz-adv-x="613" unicode="ê" glyph-name="ecircumflex"/>
<glyph d="M322 -12q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207q0 -12 -2 -40h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100zM191 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM336 595v135h146v-135h-146z M128 595v135h146v-135h-146z" horiz-adv-x="613" unicode="ë" glyph-name="edieresis"/>
<glyph d="M75 0v536h152v-536h-152zM99 595l-130 120l131 57l111 -177h-112z" horiz-adv-x="302" unicode="ì" glyph-name="igrave"/>
<glyph d="M75 0v536h152v-536h-152zM91 595l111 177l131 -57l-130 -120h-112z" horiz-adv-x="302" unicode="í" glyph-name="iacute"/>
<glyph d="M75 0v536h152v-536h-152zM-44 595l123 157h144l123 -157h-117l-79 66l-79 -66h-115z" horiz-adv-x="302" unicode="î" glyph-name="icircumflex"/>
<glyph d="M75 0v536h152v-536h-152zM182 595v135h146v-135h-146zM-26 595v135h146v-135h-146z" horiz-adv-x="302" unicode="ï" glyph-name="idieresis"/>
<glyph d="M538 653l-74 -19q157 -164 157 -341v-2q0 -127 -78.5 -215t-211.5 -88q-124 0 -206.5 81.5t-82.5 203.5v2q0 109 69 184t169 75q62 0 123 -35q-33 48 -84 98l-124 -31l-26 80l76 19q-36 29 -87 65h201l34 -28l120 30zM333 119q63 0 101 41.5t38 102.5v2q0 61 -39.5 103.5 t-101.5 42.5q-63 0 -101 -41.5t-38 -102.5v-2q0 -61 39.5 -103.5t101.5 -42.5z" horiz-adv-x="665" unicode="ð" glyph-name="eth"/>
<glyph d="M68 0v536h152v-76q67 86 157 86q86 0 134 -53t48 -146v-347h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152zM400 595q-36 0 -91 22q-43 17 -59 17q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q36 0 91 -22q43 -17 59 -17q22 0 33.5 11.5 t23.5 37.5l81 -25q-19 -69 -47 -103.5t-77 -34.5z" horiz-adv-x="623" unicode="ñ" glyph-name="ntilde"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5zM280 595 l-130 120l131 57l111 -177h-112z" horiz-adv-x="664" unicode="ò" glyph-name="ograve"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5zM272 595 l111 177l131 -57l-130 -120h-112z" horiz-adv-x="664" unicode="ó" glyph-name="oacute"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5zM137 595 l123 157h144l123 -157h-117l-79 66l-79 -66h-115z" horiz-adv-x="664" unicode="ô" glyph-name="ocircumflex"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5zM419 595 q-36 0 -91 22q-43 17 -59 17q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q36 0 91 -22q43 -17 59 -17q22 0 33.5 11.5t23.5 37.5l81 -25q-19 -69 -47 -103.5t-77 -34.5z" horiz-adv-x="664" unicode="õ" glyph-name="otilde"/>
<glyph d="M331 -12q-124 0 -206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2q0 -117 -83.5 -198t-207.5 -81zM333 119q63 0 101 42t38 104v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5zM363 595v135 h146v-135h-146zM155 595v135h146v-135h-146z" horiz-adv-x="664" unicode="ö" glyph-name="odieresis"/>
<glyph d="M242 495v144h156v-144h-156zM572 288h-504v128h504v-128zM242 65v144h156v-144h-156z" horiz-adv-x="640" unicode="÷" glyph-name="divide"/>
<glyph d="M504 541h133l-81 -93q66 -77 66 -179v-2q0 -117 -83.5 -198t-207.5 -81q-77 0 -144 35l-27 -28h-133l80 92q-65 76 -65 178v2q0 117 83.5 198t207.5 81q78 0 143 -35zM188 267q0 -37 16 -72l184 212q-29 12 -57 12q-65 0 -104 -43.5t-39 -106.5v-2zM333 115 q65 0 104 43.5t39 106.5v2q0 38 -17 72l-183 -213q27 -11 57 -11z" horiz-adv-x="664" unicode="ø" glyph-name="oslash"/>
<glyph d="M246 -10q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536h-152v76q-67 -86 -157 -86zM257 595l-130 120l131 57l111 -177h-112z" horiz-adv-x="623" unicode="ù" glyph-name="ugrave"/>
<glyph d="M246 -10q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536h-152v76q-67 -86 -157 -86zM249 595l111 177l131 -57l-130 -120h-112z" horiz-adv-x="623" unicode="ú" glyph-name="uacute"/>
<glyph d="M246 -10q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536h-152v76q-67 -86 -157 -86zM114 595l123 157h144l123 -157h-117l-79 66l-79 -66h-115z" horiz-adv-x="623" unicode="û" glyph-name="ucircumflex"/>
<glyph d="M246 -10q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536h-152v76q-67 -86 -157 -86zM340 595v135h146v-135h-146zM132 595v135h146v-135h-146z" horiz-adv-x="623" unicode="ü" glyph-name="udieresis"/>
<glyph d="M303 171l117 365h158l-206 -549q-31 -83 -70 -116.5t-106 -33.5q-72 0 -135 37l51 110q35 -21 65 -21q36 0 53 35l-210 538h161zM239 595l111 177l131 -57l-130 -120h-112z" horiz-adv-x="600" unicode="ý" glyph-name="yacute"/>
<glyph d="M68 -160v890h152v-271q64 87 167 87q100 0 172 -74.5t72 -202.5v-2q0 -128 -71.5 -202.5t-172.5 -74.5q-100 0 -167 80v-230h-152zM348 119q55 0 93 41t38 107v2q0 65 -38 106.5t-93 41.5t-92.5 -41.5t-37.5 -106.5v-2q0 -65 37.5 -106.5t92.5 -41.5z" horiz-adv-x="675" unicode="þ" glyph-name="thorn"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="asterisk" k="100"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="question" k="60"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="AE,AEacute" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="quoteright,quotedblright" k="80"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="25"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="25"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="v" k="70"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="55"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="V" k="100"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="90"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="110"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="X" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="hyphen,endash,emdash" k="40"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="guillemotleft,guilsinglleft" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="45"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="t,tcommaaccent,tcaron" k="30"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="10"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="25"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="quoteleft,quotedblleft" k="80"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g2="registered,servicemark,trademark" k="100"/>
<hkern g1="B" g2="question" k="5"/>
<hkern g1="B" g2="ampersand" k="-15"/>
<hkern g1="B" g2="v" k="10"/>
<hkern g1="B" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="B" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="B" g2="T,Tcommaaccent,Tcaron" k="10"/>
<hkern g1="B" g2="V" k="20"/>
<hkern g1="B" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g1="B" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g1="B" g2="X" k="20"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="question" k="20"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="braceright" k="20"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="bracketright" k="20"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="parenright" k="30"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="slash" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="AE,AEacute" k="50"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="50"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="J" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="Z,Zacute,Zdotaccent,Zcaron" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="T,Tcommaaccent,Tcaron" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="V" k="45"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="40"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="65"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="X" k="55"/>
<hkern g1="D,Eth,Dcaron,Dcroat" g2="x" k="10"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="slash" k="175"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="fiveeighths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="fivesixths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="fourfifths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="oneeighth" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="oneeighth.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onefifth" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onefifth.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onehalf" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onehalf.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onequarter" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onequarter.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onesixth" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onesixth.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onethird" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="onethird.alt" k="20"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="seveneighths" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="threeeighths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="threefifths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="threequarters" k="25"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="twofifths" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="twothirds" k="15"/>
<hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" g2="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num" k="25"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="question" k="10"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="-10"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="v" k="5"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="5"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="T,Tcommaaccent,Tcaron" k="10"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="V" k="25"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="20"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent" g2="X" k="10"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="asterisk" k="80"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="question" k="60"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="quoteright,quotedblright" k="40"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="5"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="v" k="60"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="50"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="60"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="T,Tcommaaccent,Tcaron" k="100"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="V" k="115"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="100"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="130"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="hyphen,endash,emdash" k="40"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="20"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="20"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="quoteleft,quotedblleft" k="40"/>
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" g2="registered,servicemark,trademark" k="90"/>
<hkern g1="P" g2="slash" k="60"/>
<hkern g1="P" g2="AE,AEacute" k="70"/>
<hkern g1="P" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="70"/>
<hkern g1="P" g2="J" k="100"/>
<hkern g1="P" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="100"/>
<hkern g1="P" g2="quoteright,quotedblright" k="-20"/>
<hkern g1="P" g2="Z,Zacute,Zdotaccent,Zcaron" k="15"/>
<hkern g1="P" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="5"/>
<hkern g1="P" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="P" g2="v" k="-10"/>
<hkern g1="P" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="-10"/>
<hkern g1="P" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="-10"/>
<hkern g1="P" g2="V" k="10"/>
<hkern g1="P" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="5"/>
<hkern g1="P" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="10"/>
<hkern g1="P" g2="X" k="30"/>
<hkern g1="P" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="-15"/>
<hkern g1="P" g2="t,tcommaaccent,tcaron" k="-15"/>
<hkern g1="hyphen,endash,emdash" g2="one" k="30"/>
<hkern g1="hyphen,endash,emdash" g2="seven" k="40"/>
<hkern g1="hyphen,endash,emdash" g2="three" k="10"/>
<hkern g1="hyphen,endash,emdash" g2="three.alt" k="10"/>
<hkern g1="hyphen,endash,emdash" g2="AE,AEacute" k="40"/>
<hkern g1="hyphen,endash,emdash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="40"/>
<hkern g1="hyphen,endash,emdash" g2="Z,Zacute,Zdotaccent,Zcaron" k="30"/>
<hkern g1="hyphen,endash,emdash" g2="v" k="15"/>
<hkern g1="hyphen,endash,emdash" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="hyphen,endash,emdash" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="hyphen,endash,emdash" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="hyphen,endash,emdash" g2="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g1="hyphen,endash,emdash" g2="V" k="40"/>
<hkern g1="hyphen,endash,emdash" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g1="hyphen,endash,emdash" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="80"/>
<hkern g1="hyphen,endash,emdash" g2="X" k="50"/>
<hkern g1="hyphen,endash,emdash" g2="x" k="30"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="one" k="50"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="seven" k="20"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="j" k="-15"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="zero" k="20"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="quoteright,quotedblright" k="40"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="20"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="v" k="85"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="70"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="60"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="T,Tcommaaccent,Tcaron" k="100"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="V" k="120"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="100"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="130"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="15"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="t,tcommaaccent,tcaron" k="25"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab" g2="quoteleft,quotedblleft" k="20"/>
<hkern g1="quoteleft,quotedblleft" g2="questiondown" k="35"/>
<hkern g1="quoteleft,quotedblleft" g2="AE,AEacute" k="90"/>
<hkern g1="quoteleft,quotedblleft" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="90"/>
<hkern g1="quoteleft,quotedblleft" g2="J" k="80"/>
<hkern g1="quoteleft,quotedblleft" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="15"/>
<hkern g1="quoteleft,quotedblleft" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="quoteleft,quotedblleft" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="quoteleft,quotedblleft" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="quoteleft,quotedblleft" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="question" k="20"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="braceright" k="20"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="bracketright" k="20"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="parenright" k="30"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="slash" k="40"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="AE,AEacute" k="45"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="45"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="J" k="30"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="40"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Z,Zacute,Zdotaccent,Zcaron" k="35"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="T,Tcommaaccent,Tcaron" k="30"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="V" k="45"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="40"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="X" k="50"/>
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="x" k="5"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="question" k="10"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="AE,AEacute" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="v" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="z,zacute,zdotaccent,zcaron" k="5"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="T,Tcommaaccent,Tcaron" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="V" k="30"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="25"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="X" k="25"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="x" k="15"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="5"/>
<hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent" g2="t,tcommaaccent,tcaron" k="5"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="ampersand" k="45"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="slash" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="j" k="10"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="AE,AEacute" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="J" k="110"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="guillemotright,guilsinglright" k="70"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="100"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="95"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="105"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="105"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="s,sacute,scedilla,scaron,scommaaccent" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="v" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="40"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="z,zacute,zdotaccent,zcaron" k="80"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="x" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="colon,semicolon,colon.tab,semicolon.tab" k="10"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="hyphen,endash,emdash" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="guillemotleft,guilsinglleft" k="90"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="30"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" k="5"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="60"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="30"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek,i.dot" k="10"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="schwa" k="85"/>
<hkern g1="T,Tcommaaccent,Tcaron" g2="Eth,Dcroat" k="10"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="slash" k="15"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="AE,AEacute" k="25"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="25"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="J" k="20"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="15"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="X" k="10"/>
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="x" k="5"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="ampersand" k="30"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="slash" k="100"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="j" k="15"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="AE,AEacute" k="90"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="90"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="J" k="105"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="guillemotright,guilsinglright" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="100"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="60"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="65"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="70"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="s,sacute,scedilla,scaron,scommaaccent" k="60"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="v" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="z,zacute,zdotaccent,zcaron" k="55"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="V" k="10"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="20"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="X" k="15"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="x" k="40"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="colon,semicolon,colon.tab,semicolon.tab" k="15"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="hyphen,endash,emdash" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="guillemotleft,guilsinglleft" k="50"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" k="10"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="35"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="30"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek,i.dot" k="15"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="t,tcommaaccent,tcaron" k="25"/>
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="35"/>
<hkern g1="X" g2="question" k="15"/>
<hkern g1="X" g2="ampersand" k="10"/>
<hkern g1="X" g2="j" k="10"/>
<hkern g1="X" g2="AE,AEacute" k="20"/>
<hkern g1="X" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g1="X" g2="J" k="10"/>
<hkern g1="X" g2="guillemotright,guilsinglright" k="20"/>
<hkern g1="X" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="40"/>
<hkern g1="X" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="45"/>
<hkern g1="X" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="X" g2="v" k="50"/>
<hkern g1="X" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="40"/>
<hkern g1="X" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="40"/>
<hkern g1="X" g2="V" k="20"/>
<hkern g1="X" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g1="X" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g1="X" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="30"/>
<hkern g1="X" g2="hyphen,endash,emdash" k="50"/>
<hkern g1="X" g2="guillemotleft,guilsinglleft" k="50"/>
<hkern g1="X" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="50"/>
<hkern g1="X" g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" k="10"/>
<hkern g1="X" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="20"/>
<hkern g1="X" g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek,i.dot" k="10"/>
<hkern g1="X" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="X" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="20"/>
<hkern g1="X" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="10"/>
<hkern g1="X" g2="schwa" k="35"/>
<hkern g1="X" g2="Eth,Dcroat" k="15"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="ampersand" k="55"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="slash" k="110"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="j" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="AE,AEacute" k="110"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="110"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="J" k="130"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="guillemotright,guilsinglright" k="75"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="130"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="105"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="110"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="100"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="s,sacute,scedilla,scaron,scommaaccent" k="100"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="v" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="55"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="z,zacute,zdotaccent,zcaron" k="80"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="V" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="35"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="X" k="30"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="x" k="70"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="colon,semicolon,colon.tab,semicolon.tab" k="40"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="hyphen,endash,emdash" k="80"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="guillemotleft,guilsinglleft" k="100"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="75"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="40"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek,i.dot" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="t,tcommaaccent,tcaron" k="30"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="75"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="schwa" k="100"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g2="Eth,Dcroat" k="15"/>
<hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" g2="asterisk" k="15"/>
<hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" g2="question" k="30"/>
<hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" g2="v" k="20"/>
<hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="b,p,thorn" g2="asterisk" k="15"/>
<hkern g1="b,p,thorn" g2="question" k="35"/>
<hkern g1="b,p,thorn" g2="braceright" k="15"/>
<hkern g1="b,p,thorn" g2="bracketright" k="20"/>
<hkern g1="b,p,thorn" g2="parenright" k="30"/>
<hkern g1="b,p,thorn" g2="guillemotright,guilsinglright" k="5"/>
<hkern g1="b,p,thorn" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="10"/>
<hkern g1="b,p,thorn" g2="v" k="25"/>
<hkern g1="b,p,thorn" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g1="b,p,thorn" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="25"/>
<hkern g1="b,p,thorn" g2="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g1="b,p,thorn" g2="x" k="30"/>
<hkern g1="b,p,thorn" g2="quoteleft,quotedblleft" k="10"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="asterisk" k="20"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="question" k="50"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="braceright" k="15"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="bracketright" k="20"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="parenright" k="30"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="guillemotright,guilsinglright" k="10"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="20"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="v" k="30"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="25"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="30"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="z,zacute,zdotaccent,zcaron" k="20"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="x" k="35"/>
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa" g2="quoteleft,quotedblleft" k="20"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="asterisk" k="15"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="question" k="35"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="v" k="20"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="20"/>
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" g2="t,tcommaaccent,tcaron" k="5"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="question" k="15"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="parenright" k="15"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="quoteright,quotedblright" k="-15"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="v" k="5"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="5"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="5"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="x" k="10"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="guillemotleft,guilsinglleft" k="10"/>
<hkern g1="c,ccedilla,cacute,cdotaccent,ccaron" g2="schwa" k="10"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="asterisk" k="20"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="question" k="40"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="braceright" k="10"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="bracketright" k="20"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="parenright" k="30"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="10"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="v" k="25"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="25"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="25"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" g2="x" k="30"/>
<hkern g1="f,f_f" g2="asterisk" k="-30"/>
<hkern g1="f,f_f" g2="question" k="-35"/>
<hkern g1="f,f_f" g2="braceright" k="-30"/>
<hkern g1="f,f_f" g2="bracketright" k="-20"/>
<hkern g1="f,f_f" g2="parenright" k="-30"/>
<hkern g1="f,f_f" g2="slash" k="45"/>
<hkern g1="f,f_f" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="45"/>
<hkern g1="f,f_f" g2="quoteright,quotedblright" k="-35"/>
<hkern g1="f,f_f" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="f,f_f" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="f,f_f" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="15"/>
<hkern g1="f,f_f" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="f,f_f" g2="guillemotleft,guilsinglleft" k="15"/>
<hkern g1="f,f_f" g2="quoteleft,quotedblleft" k="-30"/>
<hkern g1="f,f_f" g2="registered,servicemark,trademark" k="-40"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="asterisk" k="-10"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="slash" k="75"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="90"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="quoteright,quotedblright" k="-35"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="25"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="guillemotleft,guilsinglleft" k="10"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="quoteleft,quotedblleft" k="-20"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="question" k="35"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="braceright" k="10"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="bracketright" k="15"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="parenright" k="20"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="v" k="20"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="x" k="25"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="guillemotleft,guilsinglleft" k="10"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="t,tcommaaccent,tcaron" k="10"/>
<hkern g1="s,sacute,scedilla,scaron,scommaaccent" g2="quoteleft,quotedblleft" k="10"/>
<hkern g1="v" g2="question" k="10"/>
<hkern g1="v" g2="braceright" k="10"/>
<hkern g1="v" g2="bracketright" k="20"/>
<hkern g1="v" g2="slash" k="70"/>
<hkern g1="v" g2="guillemotright,guilsinglright" k="15"/>
<hkern g1="v" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="85"/>
<hkern g1="v" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="25"/>
<hkern g1="v" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="30"/>
<hkern g1="v" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="25"/>
<hkern g1="v" g2="s,sacute,scedilla,scaron,scommaaccent" k="20"/>
<hkern g1="v" g2="v" k="15"/>
<hkern g1="v" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g1="v" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="v" g2="z,zacute,zdotaccent,zcaron" k="5"/>
<hkern g1="v" g2="x" k="10"/>
<hkern g1="v" g2="hyphen,endash,emdash" k="15"/>
<hkern g1="v" g2="guillemotleft,guilsinglleft" k="30"/>
<hkern g1="v" g2="schwa" k="25"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="question" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="braceright" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="bracketright" k="20"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="slash" k="60"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="guillemotright,guilsinglright" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="70"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="20"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="25"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="20"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="v" k="15"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="z,zacute,zdotaccent,zcaron" k="5"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="x" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="hyphen,endash,emdash" k="10"/>
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" g2="guillemotleft,guilsinglleft" k="20"/>
<hkern g1="x" g2="question" k="15"/>
<hkern g1="x" g2="braceright" k="10"/>
<hkern g1="x" g2="bracketright" k="10"/>
<hkern g1="x" g2="guillemotright,guilsinglright" k="15"/>
<hkern g1="x" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="30"/>
<hkern g1="x" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="35"/>
<hkern g1="x" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="15"/>
<hkern g1="x" g2="s,sacute,scedilla,scaron,scommaaccent" k="20"/>
<hkern g1="x" g2="v" k="10"/>
<hkern g1="x" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="x" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="x" g2="hyphen,endash,emdash" k="30"/>
<hkern g1="x" g2="guillemotleft,guilsinglleft" k="45"/>
<hkern g1="x" g2="schwa" k="25"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="question" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="braceright" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="bracketright" k="20"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="slash" k="70"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="guillemotright,guilsinglright" k="15"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="85"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="25"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="30"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="25"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="s,sacute,scedilla,scaron,scommaaccent" k="20"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="v" k="15"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="z,zacute,zdotaccent,zcaron" k="5"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="x" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="hyphen,endash,emdash" k="15"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="guillemotleft,guilsinglleft" k="30"/>
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" g2="schwa" k="25"/>
<hkern g1="F" g2="AE,AEacute" k="80"/>
<hkern g1="F" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="80"/>
<hkern g1="F" g2="J" k="110"/>
<hkern g1="F" g2="guillemotright,guilsinglright" k="15"/>
<hkern g1="F" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="100"/>
<hkern g1="F" g2="quoteright,quotedblright" k="-20"/>
<hkern g1="F" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="F" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="F" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="F" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="25"/>
<hkern g1="F" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="F" g2="v" k="15"/>
<hkern g1="F" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="F" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="F" g2="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g1="Q" g2="T,Tcommaaccent,Tcaron" k="30"/>
<hkern g1="Q" g2="V" k="45"/>
<hkern g1="Q" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="40"/>
<hkern g1="Q" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="65"/>
<hkern g1="Q.alt" g2="AE,AEacute" k="45"/>
<hkern g1="Q.alt" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="45"/>
<hkern g1="Q.alt" g2="J" k="30"/>
<hkern g1="Q.alt" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="40"/>
<hkern g1="Q.alt" g2="Z,Zacute,Zdotaccent,Zcaron" k="35"/>
<hkern g1="Q.alt" g2="T,Tcommaaccent,Tcaron" k="30"/>
<hkern g1="Q.alt" g2="V" k="45"/>
<hkern g1="Q.alt" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="40"/>
<hkern g1="Q.alt" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g1="Q.alt" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g1="Q.alt" g2="X" k="50"/>
<hkern g1="Q.alt" g2="x" k="5"/>
<hkern g1="V" g2="AE,AEacute" k="100"/>
<hkern g1="V" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g1="V" g2="J" k="120"/>
<hkern g1="V" g2="guillemotright,guilsinglright" k="40"/>
<hkern g1="V" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="120"/>
<hkern g1="V" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="V" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="65"/>
<hkern g1="V" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="70"/>
<hkern g1="V" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="70"/>
<hkern g1="V" g2="s,sacute,scedilla,scaron,scommaaccent" k="60"/>
<hkern g1="V" g2="v" k="40"/>
<hkern g1="V" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="35"/>
<hkern g1="V" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="40"/>
<hkern g1="V" g2="z,zacute,zdotaccent,zcaron" k="55"/>
<hkern g1="V" g2="V" k="10"/>
<hkern g1="V" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g1="V" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g1="V" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="25"/>
<hkern g1="V" g2="X" k="20"/>
<hkern g1="V" g2="x" k="50"/>
<hkern g1="V" g2="colon,semicolon,colon.tab,semicolon.tab" k="20"/>
<hkern g1="V" g2="hyphen,endash,emdash" k="40"/>
<hkern g1="V" g2="guillemotleft,guilsinglleft" k="60"/>
<hkern g1="V" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="45"/>
<hkern g1="V" g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" k="10"/>
<hkern g1="V" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="40"/>
<hkern g1="V" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="25"/>
<hkern g1="V" g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek,i.dot" k="20"/>
<hkern g1="V" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="V" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="40"/>
<hkern g1="ampersand" g2="T,Tcommaaccent,Tcaron" k="50"/>
<hkern g1="ampersand" g2="V" k="55"/>
<hkern g1="ampersand" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="45"/>
<hkern g1="ampersand" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g1="asterisk" g2="AE,AEacute" k="100"/>
<hkern g1="asterisk" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g1="asterisk" g2="J" k="80"/>
<hkern g1="asterisk" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="15"/>
<hkern g1="asterisk" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="20"/>
<hkern g1="asterisk" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="asterisk" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="backslash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="braceleft" g2="J" k="10"/>
<hkern g1="braceleft" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="15"/>
<hkern g1="braceleft" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="braceleft" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="braceleft" g2="v" k="10"/>
<hkern g1="braceleft" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="braceleft" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="braceleft" g2="z,zacute,zdotaccent,zcaron" k="10"/>
<hkern g1="braceleft" g2="x" k="10"/>
<hkern g1="braceleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="20"/>
<hkern g1="bracketleft" g2="J" k="10"/>
<hkern g1="bracketleft" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="20"/>
<hkern g1="bracketleft" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="20"/>
<hkern g1="bracketleft" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="bracketleft" g2="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g1="bracketleft" g2="v" k="20"/>
<hkern g1="bracketleft" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g1="bracketleft" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="bracketleft" g2="x" k="10"/>
<hkern g1="bracketleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="20"/>
<hkern g1="fiveeighths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="fivesixths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="fourfifths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="germandbls" g2="v" k="10"/>
<hkern g1="germandbls" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="5"/>
<hkern g1="germandbls" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="germandbls" g2="x" k="10"/>
<hkern g1="nine" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="10"/>
<hkern g1="oneeighth" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="oneeighth.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onefifth" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onefifth.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onehalf" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onehalf.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onequarter" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="25"/>
<hkern g1="onequarter.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="25"/>
<hkern g1="onesixth" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onesixth.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onethird" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="onethird.alt" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="parenleft" g2="J" k="15"/>
<hkern g1="parenleft" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="30"/>
<hkern g1="parenleft" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="30"/>
<hkern g1="parenleft" g2="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g1="parenleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="30"/>
<hkern g1="questiondown" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="-10"/>
<hkern g1="questiondown" g2="v" k="45"/>
<hkern g1="questiondown" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="35"/>
<hkern g1="questiondown" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="35"/>
<hkern g1="questiondown" g2="T,Tcommaaccent,Tcaron" k="60"/>
<hkern g1="questiondown" g2="V" k="60"/>
<hkern g1="questiondown" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="50"/>
<hkern g1="questiondown" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="70"/>
<hkern g1="questiondown" g2="X" k="10"/>
<hkern g1="questiondown" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="20"/>
<hkern g1="questiondown" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="10"/>
<hkern g1="questiondown" g2="t,tcommaaccent,tcaron" k="15"/>
<hkern g1="questiondown" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g1="seven" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="100"/>
<hkern g1="seven" g2="hyphen,endash,emdash" k="30"/>
<hkern g1="seveneighths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="slash" g2="AE,AEacute" k="120"/>
<hkern g1="slash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="120"/>
<hkern g1="slash" g2="J" k="130"/>
<hkern g1="slash" g2="Z,Zacute,Zdotaccent,Zcaron" k="20"/>
<hkern g1="slash" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="70"/>
<hkern g1="slash" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="80"/>
<hkern g1="slash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="65"/>
<hkern g1="slash" g2="s,sacute,scedilla,scaron,scommaaccent" k="85"/>
<hkern g1="slash" g2="v" k="50"/>
<hkern g1="slash" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="50"/>
<hkern g1="slash" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g1="slash" g2="z,zacute,zdotaccent,zcaron" k="60"/>
<hkern g1="slash" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="30"/>
<hkern g1="slash" g2="x" k="50"/>
<hkern g1="slash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="slash" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="50"/>
<hkern g1="slash" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="25"/>
<hkern g1="slash" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="slash" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="50"/>
<hkern g1="slash" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="175"/>
<hkern g1="slash.tab" g2="AE,AEacute" k="120"/>
<hkern g1="slash.tab" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="120"/>
<hkern g1="slash.tab" g2="J" k="130"/>
<hkern g1="slash.tab" g2="Z,Zacute,Zdotaccent,Zcaron" k="20"/>
<hkern g1="slash.tab" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="70"/>
<hkern g1="slash.tab" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="80"/>
<hkern g1="slash.tab" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="65"/>
<hkern g1="slash.tab" g2="s,sacute,scedilla,scaron,scommaaccent" k="85"/>
<hkern g1="slash.tab" g2="v" k="50"/>
<hkern g1="slash.tab" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="50"/>
<hkern g1="slash.tab" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g1="slash.tab" g2="z,zacute,zdotaccent,zcaron" k="60"/>
<hkern g1="slash.tab" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="30"/>
<hkern g1="slash.tab" g2="x" k="50"/>
<hkern g1="slash.tab" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="40"/>
<hkern g1="slash.tab" g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" k="50"/>
<hkern g1="slash.tab" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="25"/>
<hkern g1="slash.tab" g2="t,tcommaaccent,tcaron" k="20"/>
<hkern g1="slash.tab" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="50"/>
<hkern g1="slash.tab" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="175"/>
<hkern g1="threeeighths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="threefifths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="threequarters" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="25"/>
<hkern g1="twofifths" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="twothirds" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="15"/>
<hkern g1="zero" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="20"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="J" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="v" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="V" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="X" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="x" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="hyphen,endash,emdash" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="20"/>
<hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" g2="schwa" k="10"/>
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="10"/>
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" g2="v" k="10"/>
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g1="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den" k="25"/>
<hkern g1="zero.inf,one.inf,two.inf,three.inf,four.inf,five.inf,six.inf,seven.inf,eight.inf,nine.inf,one.inf_alt" g2="zero.inf,one.inf,two.inf,three.inf,four.inf,five.inf,six.inf,seven.inf,eight.inf,nine.inf,one.inf_alt" k="25"/>
<hkern g1="two.sup,three.sup,one.sup,zero.sup,four.sup,five.sup,six.sup,seven.sup,eight.sup,nine.sup,one.sup_alt" g2="two.sup,three.sup,one.sup,zero.sup,four.sup,five.sup,six.sup,seven.sup,eight.sup,nine.sup,one.sup_alt" k="25"/>
<hkern g1="J" g2="AE,AEacute" k="25"/>
<hkern g1="J" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="25"/>
<hkern g1="J" g2="J" k="20"/>
<hkern g1="J" g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab" k="15"/>
<hkern g1="K,Kcommaaccent" g2="AE,AEacute" k="20"/>
<hkern g1="K,Kcommaaccent" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g1="K,Kcommaaccent" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="25"/>
<hkern g1="K,Kcommaaccent" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="30"/>
<hkern g1="K,Kcommaaccent" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="K,Kcommaaccent" g2="v" k="60"/>
<hkern g1="K,Kcommaaccent" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="50"/>
<hkern g1="K,Kcommaaccent" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g1="K,Kcommaaccent" g2="T,Tcommaaccent,Tcaron" k="10"/>
<hkern g1="K,Kcommaaccent" g2="V" k="30"/>
<hkern g1="K,Kcommaaccent" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="30"/>
<hkern g1="K,Kcommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g1="K,Kcommaaccent" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="K,Kcommaaccent" g2="hyphen,endash,emdash" k="50"/>
<hkern g1="K,Kcommaaccent" g2="guillemotleft,guilsinglleft" k="20"/>
<hkern g1="K,Kcommaaccent" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="55"/>
<hkern g1="K,Kcommaaccent" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="20"/>
<hkern g1="K,Kcommaaccent" g2="t,tcommaaccent,tcaron" k="25"/>
<hkern g1="K,Kcommaaccent" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="20"/>
<hkern g1="K,Kcommaaccent" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g1="K,Kcommaaccent" g2="schwa" k="20"/>
<hkern g1="colon,semicolon,colon.tab,semicolon.tab" g2="T,Tcommaaccent,Tcaron" k="10"/>
<hkern g1="colon,semicolon,colon.tab,semicolon.tab" g2="V" k="20"/>
<hkern g1="colon,semicolon,colon.tab,semicolon.tab" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g1="colon,semicolon,colon.tab,semicolon.tab" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="40"/>
<hkern g1="guillemotleft,guilsinglleft" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="5"/>
<hkern g1="guillemotleft,guilsinglleft" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="guillemotleft,guilsinglleft" g2="v" k="15"/>
<hkern g1="guillemotleft,guilsinglleft" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g1="guillemotleft,guilsinglleft" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="guillemotleft,guilsinglleft" g2="T,Tcommaaccent,Tcaron" k="70"/>
<hkern g1="guillemotleft,guilsinglleft" g2="V" k="40"/>
<hkern g1="guillemotleft,guilsinglleft" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g1="guillemotleft,guilsinglleft" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="75"/>
<hkern g1="guillemotleft,guilsinglleft" g2="X" k="20"/>
<hkern g1="guillemotleft,guilsinglleft" g2="x" k="15"/>
<hkern g1="guillemotright,guilsinglright" g2="AE,AEacute" k="20"/>
<hkern g1="guillemotright,guilsinglright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g1="guillemotright,guilsinglright" g2="Z,Zacute,Zdotaccent,Zcaron" k="15"/>
<hkern g1="guillemotright,guilsinglright" g2="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g1="guillemotright,guilsinglright" g2="v" k="30"/>
<hkern g1="guillemotright,guilsinglright" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g1="guillemotright,guilsinglright" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="30"/>
<hkern g1="guillemotright,guilsinglright" g2="z,zacute,zdotaccent,zcaron" k="20"/>
<hkern g1="guillemotright,guilsinglright" g2="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g1="guillemotright,guilsinglright" g2="V" k="60"/>
<hkern g1="guillemotright,guilsinglright" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="50"/>
<hkern g1="guillemotright,guilsinglright" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="100"/>
<hkern g1="guillemotright,guilsinglright" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="guillemotright,guilsinglright" g2="X" k="50"/>
<hkern g1="guillemotright,guilsinglright" g2="x" k="45"/>
<hkern g1="guillemotright,guilsinglright" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="10"/>
<hkern g1="guillemotright,guilsinglright" g2="t,tcommaaccent,tcaron" k="10"/>
<hkern g1="quoteright,quotedblright" g2="AE,AEacute" k="100"/>
<hkern g1="quoteright,quotedblright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g1="quoteright,quotedblright" g2="J" k="100"/>
<hkern g1="quoteright,quotedblright" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="30"/>
<hkern g1="quoteright,quotedblright" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="35"/>
<hkern g1="quoteright,quotedblright" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="20"/>
<hkern g1="quoteright,quotedblright" g2="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="J" k="5"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="5"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="10"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="V" k="20"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="25"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="20"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="25"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="v" k="20"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="hyphen,endash,emdash" k="30"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="guillemotleft,guilsinglleft" k="20"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" k="35"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="f,f_f,fi,fl,f_f_i,f_f_l" k="10"/>
<hkern g1="k,kcommaaccent" g2="guillemotright,guilsinglright" k="10"/>
<hkern g1="k,kcommaaccent" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="25"/>
<hkern g1="k,kcommaaccent" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="25"/>
<hkern g1="k,kcommaaccent" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" k="10"/>
<hkern g1="k,kcommaaccent" g2="v" k="20"/>
<hkern g1="k,kcommaaccent" g2="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g1="k,kcommaaccent" g2="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g1="k,kcommaaccent" g2="hyphen,endash,emdash" k="20"/>
<hkern g1="k,kcommaaccent" g2="guillemotleft,guilsinglleft" k="20"/>
<hkern g1="k,kcommaaccent" g2="t,tcommaaccent,tcaron" k="10"/>
<hkern g1="k,kcommaaccent" g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" k="10"/>
<hkern g1="k,kcommaaccent" g2="schwa" k="20"/>
<hkern g1="t,tcommaaccent,tcaron" g2="quoteright,quotedblright" k="-10"/>
<hkern g1="t,tcommaaccent,tcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="15"/>
<hkern g1="t,tcommaaccent,tcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="t,tcommaaccent,tcaron" g2="guillemotleft,guilsinglleft" k="10"/>
<hkern g1="z,zacute,zdotaccent,zcaron" g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="15"/>
<hkern g1="z,zacute,zdotaccent,zcaron" g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" k="15"/>
<hkern g1="z,zacute,zdotaccent,zcaron" g2="guillemotleft,guilsinglleft" k="15"/>
</font>
</defs>
</svg>
