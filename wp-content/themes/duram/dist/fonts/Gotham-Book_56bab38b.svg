<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg  PUBLIC '-//W3C//DTD SVG 1.1//EN'  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
<defs>
<font id="Gotham-Book" horiz-adv-x="790">
<font-face cap-height="700" panose-1="2 0 6 4 4 0 0 2 0 4" font-family="m" underline-position="-153" descent="-200" ascent="800" bbox="-21 -163 1063 901" units-per-em="1000" underline-thickness="20" font-weight="325" x-height="517" unicode-range="U+0020-00FE"/>
<missing-glyph/>
<glyph unicode=" " glyph-name="space" horiz-adv-x="300"/>
<glyph unicode="!" glyph-name="exclam" d="M114 198l-23 445v57h90v-57l-24 -445h-43zM90 0v105h92v-105h-92z" horiz-adv-x="271"/>
<glyph unicode='&quot;' glyph-name="quotedbl" d="M260 428l31 272h92v-5l-81 -267h-42zM65 428l30 272h93v-5l-81 -267h-42z" horiz-adv-x="440"/>
<glyph unicode="#" glyph-name="numbersign" d="M124 0l30 177h-109v69h121l37 212h-119v69h130l30 173h70l-29 -173h190l30 173h70l-29 -173h108v-69h-120l-37 -212h118v-69h-129l-31 -177h-70l30 177h-190l-31 -177h-70zM237 246h190l36 212h-190z" horiz-adv-x="700"/>
<glyph unicode="$" glyph-name="dollar" d="M292 -98v100q-129 12 -236 103l45 60q94 -84 194 -95v250q-112 27 -162.5 72t-50.5 122v2q0 77 58.5 128t151.5 56v58h64v-60q100 -9 191 -80l-43 -60q-68 58 -151 72v-246q114 -26 165 -72t51 -122v-2q0 -80 -59 -131t-154 -57v-98h-64zM295 398v235q-62 -2 -99 -33.5 t-37 -78.5v-2q0 -45 29 -73t107 -48zM353 67q63 3 101 35t38 81v2q0 46 -29.5 74t-109.5 47v-239z" horiz-adv-x="636"/>
<glyph unicode="%" glyph-name="percent" d="M210 350q-69 0 -113.5 52t-44.5 125v2q0 73 45 126t115 53q69 0 113 -52t44 -125v-2q0 -73 -45 -126t-114 -53zM124 0l514 700h71l-513 -700h-72zM622 -8q-69 0 -113 52t-44 125v2q0 73 45 126t114 53t113 -52t44 -125v-2q0 -74 -44.5 -126.5t-114.5 -52.5zM212 406 q40 0 65.5 35t25.5 86v2q0 52 -27 87.5t-66 35.5q-40 0 -65.5 -35t-25.5 -86v-2q0 -52 27 -87.5t66 -35.5zM624 48q40 0 65.5 35t25.5 86v2q0 52 -27 87.5t-66 35.5q-40 0 -65.5 -35t-25.5 -86v-2q0 -52 27 -87.5t66 -35.5z" horiz-adv-x="834"/>
<glyph unicode="&amp;" glyph-name="ampersand" d="M604 -14l-110 113q-101 -109 -229 -109q-95 0 -157.5 54.5t-62.5 141.5v2q0 136 169 202q-70 82 -70 154v2q0 70 51.5 118t133.5 48q73 0 121.5 -46t48.5 -112v-2q0 -124 -165 -182l160 -163q45 65 84 156l65 -30q-53 -113 -101 -176l122 -125zM290 415q135 46 135 133v2 q0 42 -28 69.5t-71 27.5q-47 0 -76.5 -29t-29.5 -72v-2q0 -31 15 -58.5t55 -70.5zM271 57q92 0 175 91l-187 193q-67 -25 -100.5 -64.5t-33.5 -85.5v-2q0 -57 41 -94.5t105 -37.5z" horiz-adv-x="696"/>
<glyph unicode="&apos;" glyph-name="quotesingle" d="M65 428l30 272h94v-5l-81 -267h-43z" horiz-adv-x="245"/>
<glyph unicode="(" glyph-name="parenleft" d="M345 -141q-277 156 -277 428t277 428l33 -51q-232 -148 -232 -377t232 -377z" horiz-adv-x="434"/>
<glyph unicode=")" glyph-name="parenright" d="M89 -141l-33 51q232 148 232 377t-232 377l33 51q277 -156 277 -428t-277 -428z" horiz-adv-x="434"/>
<glyph unicode="*" glyph-name="asterisk" d="M189 397l10 125l-103 -73l-27 47l116 53l-116 54l27 46l103 -72l-10 125h52l-10 -125l103 72l27 -46l-116 -54l116 -53l-27 -47l-103 73l10 -125h-52z" horiz-adv-x="430"/>
<glyph unicode="+" glyph-name="plus" d="M275 110v206h-207v72h207v206h75v-206h207v-72h-207v-206h-75z" horiz-adv-x="625"/>
<glyph unicode="," glyph-name="comma" d="M58 -122l-12 35q73 26 67 87h-36v105h92v-90q0 -60 -26.5 -92t-84.5 -45z" horiz-adv-x="245"/>
<glyph unicode="-" glyph-name="hyphen" d="M65 260v81h278v-81h-278z" horiz-adv-x="408"/>
<glyph unicode="." glyph-name="period" d="M77 0v105h92v-105h-92z" horiz-adv-x="245"/>
<glyph unicode="/" glyph-name="slash" d="M-21 -128l464 926h71l-464 -926h-71z" horiz-adv-x="508"/>
<glyph unicode="0" glyph-name="zero" d="M357 -12q-132 0 -213.5 104.5t-81.5 255.5v2q0 151 82.5 256.5t214.5 105.5t213 -104.5t81 -255.5v-2q0 -151 -82 -256.5t-214 -105.5zM359 60q95 0 153.5 84.5t58.5 203.5v2q0 119 -59.5 204.5t-154.5 85.5t-154 -84t-59 -204v-2q0 -119 60 -204.5t155 -85.5z" horiz-adv-x="715"/>
<glyph unicode="1" glyph-name="one" d="M179 0v623l-132 -41l-19 64l171 59h58v-705h-78z" horiz-adv-x="358"/>
<glyph unicode="2" glyph-name="two" d="M48 0v61l254 224q82 73 114 120.5t32 99.5q0 60 -41 97t-100 37q-57 0 -100.5 -29t-87.5 -91l-58 42q51 76 108 112.5t144 36.5q94 0 155.5 -56t61.5 -142v-2q0 -73 -39 -129.5t-138 -142.5l-187 -166h371v-72h-489z" horiz-adv-x="599"/>
<glyph unicode="3" glyph-name="three" d="M311 -12q-165 0 -261 125l57 51q85 -104 205 -104q65 0 109 38t44 98v2q0 64 -54 99.5t-142 35.5h-45l-16 47l221 249h-342v71h447v-58l-220 -244q100 -8 165.5 -57.5t65.5 -139.5v-2q0 -92 -67.5 -151.5t-166.5 -59.5z" horiz-adv-x="613"/>
<glyph unicode="4" glyph-name="four" d="M432 0v166h-369l-20 57l396 482h69v-473h109v-66h-109v-166h-76zM138 232h294v361z" horiz-adv-x="671"/>
<glyph unicode="5" glyph-name="five" d="M303 -12q-139 0 -250 111l52 57q99 -96 199 -96q74 0 121.5 44t47.5 111v2q0 65 -48.5 106t-125.5 41q-72 0 -147 -40l-53 35l20 341h403v-73h-334l-15 -224q71 31 138 31q105 0 173.5 -58t68.5 -156v-2q0 -102 -70 -166t-180 -64z" horiz-adv-x="612"/>
<glyph unicode="6" glyph-name="six" d="M333 -12q-113 0 -184 71q-45 45 -66.5 107t-21.5 164v2q0 164 78.5 272t212.5 108q109 0 207 -80l-46 -61q-82 69 -165 69q-94 0 -150 -86t-55 -223q73 107 199 107q101 0 171.5 -59.5t70.5 -155.5v-2q0 -101 -72 -167t-179 -66zM334 58q76 0 122.5 46t46.5 114v2 q0 65 -48 107.5t-123 42.5q-76 0 -125.5 -46t-49.5 -107v-2q0 -67 50 -112t127 -45z" horiz-adv-x="647"/>
<glyph unicode="7" glyph-name="seven" d="M137 0l316 628h-385v72h475v-59l-318 -641h-88z" horiz-adv-x="596"/>
<glyph unicode="8" glyph-name="eight" d="M315 -10q-113 0 -188 55.5t-75 142.5v2q0 60 39 104t105 68q-120 55 -120 161v2q0 79 70.5 132t168.5 53t168.5 -53t70.5 -132v-2q0 -106 -120 -161q66 -25 105 -68t39 -103v-2q0 -88 -75 -143.5t-188 -55.5zM315 390q68 0 114 36t46 92v2q0 52 -46 86.5t-114 34.5 q-69 0 -114.5 -34.5t-45.5 -86.5v-2q0 -56 46 -92t114 -36zM315 59q84 0 133.5 38t49.5 93v2q0 58 -53 95.5t-130 37.5t-130 -37.5t-53 -95.5v-2q0 -55 49.5 -93t133.5 -38z" horiz-adv-x="630"/>
<glyph unicode="9" glyph-name="nine" d="M295 -12q-112 0 -216 86l46 61q85 -75 172 -75q94 0 152 86t56 221q-70 -112 -198 -112q-108 0 -176 61t-68 156v2q0 101 71 169.5t181 68.5q112 0 184 -72q87 -87 87 -271v-2q0 -167 -81 -273t-210 -106zM316 323q77 0 125.5 47.5t48.5 110.5v2q0 68 -50 113.5 t-128 45.5q-76 0 -122 -48t-46 -116v-2q0 -67 48 -110t124 -43z" horiz-adv-x="647"/>
<glyph unicode=":" glyph-name="colon" d="M82 412v105h92v-105h-92zM82 0v105h92v-105h-92z" horiz-adv-x="255"/>
<glyph unicode=";" glyph-name="semicolon" d="M82 412v105h92v-105h-92zM63 -122l-12 35q73 26 67 87h-36v105h92v-90q0 -60 -26.5 -92t-84.5 -45z" horiz-adv-x="255"/>
<glyph unicode="&lt;" glyph-name="less" d="M534 86l-470 233v66l470 233v-77l-386 -188l386 -189v-78z" horiz-adv-x="625"/>
<glyph unicode="=" glyph-name="equal" d="M80 432v74h465v-74h-465zM80 198v74h465v-74h-465z" horiz-adv-x="625"/>
<glyph unicode="&gt;" glyph-name="greater" d="M92 86v77l385 188l-385 189v78l469 -233v-66z" horiz-adv-x="625"/>
<glyph unicode="?" glyph-name="question" d="M220 198l-15 162l5 5q96 2 147.5 39.5t51.5 101.5v2q0 55 -40 92.5t-106 37.5q-101 0 -182 -94l-51 49q96 116 235 116q100 0 162 -56t62 -142v-2q0 -89 -58 -139.5t-150 -63.5l-10 -108h-51zM199 0v105h92v-105h-92z" horiz-adv-x="537"/>
<glyph unicode="@" glyph-name="at" d="M499 -162q-190 0 -318 127.5t-128 308.5q0 180 127.5 309t310.5 129q181 0 308.5 -118.5t127.5 -277.5q0 -119 -55.5 -182t-131.5 -63q-53 0 -89 22.5t-51 61.5q-71 -84 -165 -84q-75 0 -127 50.5t-52 131.5q0 103 71.5 176t159.5 73q99 0 146 -83l13 69l67 -9l-40 -227 q-7 -39 -7 -61q0 -38 22.5 -59.5t62.5 -21.5q58 0 99 53t41 153q0 142 -116.5 252t-283.5 110q-171 0 -286.5 -118t-115.5 -286q0 -169 116 -285.5t294 -116.5q133 0 249 68l16 -27q-123 -75 -265 -75zM450 133q64 0 114 54.5t50 125.5q0 55 -34.5 91t-87.5 36 q-65 0 -114.5 -53t-49.5 -129q0 -58 34 -91.5t88 -33.5z" horiz-adv-x="980"/>
<glyph unicode="A" glyph-name="A" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357z"/>
<glyph unicode="B" glyph-name="B" d="M99 0v700h298q119 0 183 -62q45 -47 45 -112v-2q0 -114 -111 -162q150 -45 150 -169v-2q0 -89 -69 -140t-185 -51h-311zM177 388h207q73 0 117 32.5t44 92.5v2q0 53 -40.5 83.5t-114.5 30.5h-213v-241zM177 71h235q80 0 126 33.5t46 91.5v2q0 58 -47.5 89t-138.5 31h-221 v-247z" horiz-adv-x="722"/>
<glyph unicode="C" glyph-name="C" d="M419 -12q-150 0 -250.5 104t-100.5 256v2q0 151 101 256.5t253 105.5q89 0 152 -28t121 -83l-54 -58q-101 96 -220 96q-116 0 -193.5 -83t-77.5 -204v-2q0 -122 78 -205.5t193 -83.5q68 0 120 25t106 76l52 -51q-60 -61 -125 -92t-155 -31z" horiz-adv-x="738"/>
<glyph unicode="D" glyph-name="D" d="M99 0v700h243q163 0 267.5 -99.5t104.5 -248.5v-2q0 -150 -104.5 -250t-267.5 -100h-243zM178 73h164q130 0 210 78t80 197v2q0 119 -80 198t-210 79h-164v-554z" horiz-adv-x="782"/>
<glyph unicode="E" glyph-name="E" d="M99 0v700h506v-72h-427v-239h382v-72h-382v-245h432v-72h-511z" horiz-adv-x="670"/>
<glyph unicode="F" glyph-name="F" d="M99 0v700h503v-73h-424v-250h379v-72h-379v-305h-79z" horiz-adv-x="656"/>
<glyph unicode="G" glyph-name="G" d="M424 -12q-161 0 -258.5 103t-97.5 257v2q0 148 99 255t252 107q84 0 144.5 -23t116.5 -70l-51 -60q-92 80 -213 80q-114 0 -190 -84t-76 -203v-2q0 -127 75.5 -209t201.5 -82q116 0 202 70v174h-213v71h289v-279q-120 -107 -281 -107z" horiz-adv-x="784"/>
<glyph unicode="H" glyph-name="H" d="M99 0v700h79v-311h404v311h79v-700h-79v315h-404v-315h-79z" horiz-adv-x="760"/>
<glyph unicode="I" glyph-name="I" d="M106 0v700h79v-700h-79z" horiz-adv-x="291"/>
<glyph unicode="J" glyph-name="J" d="M247 -10q-140 0 -216 124l57 49q34 -51 70.5 -75.5t89.5 -24.5q61 0 99 43.5t38 124.5v469h80v-468q0 -120 -64 -184q-60 -58 -154 -58z" horiz-adv-x="553"/>
<glyph unicode="K" glyph-name="K" d="M99 0v700h79v-418l402 418h102l-300 -306l313 -394h-99l-269 339l-149 -151v-188h-79z" horiz-adv-x="719"/>
<glyph unicode="L" glyph-name="L" d="M99 0v700h79v-627h394v-73h-473z" horiz-adv-x="619"/>
<glyph unicode="M" glyph-name="M" d="M99 0v700h80l255 -382l255 382h80v-700h-79v567l-255 -375h-4l-255 374v-566h-77z" horiz-adv-x="868"/>
<glyph unicode="N" glyph-name="N" d="M99 0v700h74l441 -561v561h77v-700h-63l-452 574v-574h-77z"/>
<glyph unicode="O" glyph-name="O" d="M424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="P" glyph-name="P" d="M99 0v700h262q119 0 190.5 -59.5t71.5 -162.5v-2q0 -109 -78.5 -169t-196.5 -60h-170v-247h-79zM178 319h173q87 0 139.5 42.5t52.5 111.5v2q0 73 -50.5 112.5t-137.5 39.5h-177v-308z" horiz-adv-x="668"/>
<glyph unicode="Q" glyph-name="Q" d="M737 -21l-92 84q-94 -75 -221 -75q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -135 -84 -235l93 -77zM426 61q92 0 161 53l-130 111l53 59l129 -117q61 79 61 181v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2 q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="R" glyph-name="R" d="M99 0v700h301q129 0 199 -70q54 -54 54 -138v-2q0 -84 -52 -136t-138 -68l215 -286h-97l-203 272h-200v-272h-79zM178 343h215q80 0 130 39t50 105v2q0 65 -47.5 101.5t-131.5 36.5h-216v-284z" horiz-adv-x="723"/>
<glyph unicode="S" glyph-name="S" d="M339 -10q-161 0 -286 112l49 58q56 -51 112.5 -75t127.5 -24q70 0 113 33.5t43 85.5v2q0 51 -38 81t-144 52q-124 27 -179.5 72.5t-55.5 129.5v2q0 83 65 137t165 54q139 0 246 -86l-46 -61q-93 76 -202 76q-67 0 -108 -32.5t-41 -80.5v-2q0 -52 39.5 -82t150.5 -54 q119 -26 173.5 -72.5t54.5 -125.5v-2q0 -89 -67 -143.5t-172 -54.5z" horiz-adv-x="640"/>
<glyph unicode="T" glyph-name="T" d="M284 0v627h-235v73h550v-73h-235v-627h-80z" horiz-adv-x="648"/>
<glyph unicode="U" glyph-name="U" d="M379 -11q-133 0 -212 79.5t-79 224.5v407h79v-402q0 -113 56.5 -174.5t157.5 -61.5q99 0 155 59t56 172v407h79v-401q0 -150 -79 -230t-213 -80z" horiz-adv-x="759"/>
<glyph unicode="V" glyph-name="V" d="M340 -5l-301 705h88l249 -603l250 603h85l-301 -705h-70z" horiz-adv-x="750"/>
<glyph unicode="W" glyph-name="W" d="M295 -5l-250 705h86l200 -583l192 585h65l192 -585l200 583h83l-250 -705h-67l-192 568l-193 -568h-66z" horiz-adv-x="1107"/>
<glyph unicode="X" glyph-name="X" d="M46 0l269 357l-259 343h93l215 -286l213 286h92l-259 -342l269 -358h-94l-224 301l-224 -301h-91z" horiz-adv-x="725"/>
<glyph unicode="Y" glyph-name="Y" d="M318 0v277l-291 423h96l236 -350l238 350h92l-291 -422v-278h-80z" horiz-adv-x="716"/>
<glyph unicode="Z" glyph-name="Z" d="M67 0v53l453 576h-438v71h546v-53l-454 -576h454v-71h-561z" horiz-adv-x="693"/>
<glyph unicode="[" glyph-name="bracketleft" d="M94 -130v830h286v-60h-213v-710h213v-60h-286z" horiz-adv-x="439"/>
<glyph unicode="\" glyph-name="backslash" d="M458 -128l-464 926h71l464 -926h-71z" horiz-adv-x="508"/>
<glyph unicode="]" glyph-name="bracketright" d="M59 -130v60h214v710h-214v60h286v-830h-286z" horiz-adv-x="439"/>
<glyph unicode="^" glyph-name="asciicircum" d="M65 493l155 209h60l155 -209h-68l-118 154l-118 -154h-66z" horiz-adv-x="500"/>
<glyph unicode="_" glyph-name="underscore" d="M-2 -160v63h604v-63h-604z" horiz-adv-x="600"/>
<glyph unicode="`" glyph-name="grave" d="M258 595l-114 108l80 37l94 -145h-60z" horiz-adv-x="500"/>
<glyph unicode="a" glyph-name="a" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28z" horiz-adv-x="579"/>
<glyph unicode="b" glyph-name="b" d="M361 -11q-122 0 -198 110v-99h-77v730h77v-317q78 115 198 115q99 0 173 -73t74 -195v-2q0 -121 -74 -195t-173 -74zM347 58q78 0 130 54t52 145v2q0 89 -53 144.5t-129 55.5q-75 0 -131 -56.5t-56 -142.5v-2q0 -87 56 -143.5t131 -56.5z" horiz-adv-x="664"/>
<glyph unicode="c" glyph-name="c" d="M319 -12q-112 0 -188.5 79t-76.5 189v2q0 111 76.5 190.5t188.5 79.5q120 0 207 -92l-51 -54q-74 78 -157 78q-78 0 -131.5 -58t-53.5 -142v-2q0 -84 54.5 -142.5t135.5 -58.5q86 0 158 77l49 -46q-90 -100 -211 -100z" horiz-adv-x="571"/>
<glyph unicode="d" glyph-name="d" d="M303 -11q-99 0 -173 73t-74 195v2q0 121 74 195t173 74q122 0 198 -110v312h77v-730h-77v104q-78 -115 -198 -115zM317 58q75 0 131 56.5t56 143.5v2q0 86 -56 142.5t-131 56.5q-78 0 -130 -54t-52 -145v-2q0 -89 53 -144.5t129 -55.5z" horiz-adv-x="664"/>
<glyph unicode="e" glyph-name="e" d="M132 227q8 -79 59.5 -125.5t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-109 0 -183.5 75.5t-74.5 194.5q0 113 71 191.5t177 78.5q111 0 176 -78t65 -196q0 -15 -1 -27h-410zM132 289h333q-7 75 -49.5 124.5t-115.5 49.5q-66 0 -113 -49t-55 -125z" horiz-adv-x="592"/>
<glyph unicode="f" glyph-name="f" d="M115 0v449h-71v67h71v45q0 88 45 133q40 40 111 40q49 0 85 -13v-67q-45 13 -75 13q-90 0 -90 -110v-42h164v-66h-163v-449h-77z" horiz-adv-x="367"/>
<glyph unicode="g" glyph-name="g" d="M315 -162q-131 0 -235 72l35 60q91 -66 199 -66q87 0 137.5 47.5t50.5 136.5v61q-82 -109 -203 -109q-98 0 -170.5 67.5t-72.5 174.5v2q0 109 73 176.5t170 67.5q124 0 202 -104v93h77v-427q0 -117 -66 -183q-69 -69 -197 -69zM314 108q76 0 133 49.5t57 125.5v2 q0 77 -56.5 126t-133.5 49q-75 0 -127 -48t-52 -126v-2q0 -76 52.5 -126t126.5 -50z" horiz-adv-x="664"/>
<glyph unicode="h" glyph-name="h" d="M86 0v730h77v-303q61 101 177 101q92 0 145 -56.5t53 -150.5v-321h-77v302q0 73 -37.5 114.5t-105.5 41.5q-67 0 -111 -45t-44 -117v-296h-77z" horiz-adv-x="616"/>
<glyph unicode="i" glyph-name="i" d="M88 630v85h88v-85h-88zM93 0v517h77v-517h-77z" horiz-adv-x="263"/>
<glyph unicode="j" glyph-name="j" d="M88 630v85h88v-85h-88zM44 -163q-27 0 -48 5v63q18 -3 38 -3q59 0 59 69v546h77v-547q0 -67 -33 -100t-93 -33z" horiz-adv-x="263"/>
<glyph unicode="k" glyph-name="k" d="M86 0v730h77v-504l279 291h96l-217 -222l224 -295h-92l-185 241l-105 -106v-135h-77z" horiz-adv-x="569"/>
<glyph unicode="l" glyph-name="l" d="M93 0v730h77v-730h-77z" horiz-adv-x="263"/>
<glyph unicode="m" glyph-name="m" d="M86 0v517h77v-87q65 98 167 98q116 0 166 -103q67 103 182 103q89 0 140.5 -56t51.5 -152v-320h-77v302q0 75 -35.5 115.5t-98.5 40.5q-61 0 -102 -43t-41 -117v-298h-76v304q0 72 -35.5 113t-97.5 41t-103 -46t-41 -117v-295h-77z" horiz-adv-x="951"/>
<glyph unicode="n" glyph-name="n" d="M86 0v517h77v-90q61 101 177 101q92 0 145 -56.5t53 -150.5v-321h-77v302q0 73 -37.5 114.5t-105.5 41.5q-67 0 -111 -45t-44 -117v-296h-77z" horiz-adv-x="616"/>
<glyph unicode="o" glyph-name="o" d="M322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5t-53.5 -141.5v-2q0 -84 55 -142.5t136 -58.5z" horiz-adv-x="646"/>
<glyph unicode="p" glyph-name="p" d="M86 -160v677h77v-104q78 115 198 115q99 0 173 -73t74 -195v-2q0 -121 -74 -195t-173 -74q-122 0 -198 110v-259h-77zM347 58q78 0 130 54t52 145v2q0 89 -53 144.5t-129 55.5q-75 0 -131 -56.5t-56 -142.5v-2q0 -87 56 -143.5t131 -56.5z" horiz-adv-x="664"/>
<glyph unicode="q" glyph-name="q" d="M501 -160v264q-78 -115 -198 -115q-99 0 -173 73t-74 195v2q0 121 74 195t173 74q122 0 198 -110v99h77v-677h-77zM317 58q75 0 131 56.5t56 143.5v2q0 86 -56 142.5t-131 56.5q-78 0 -130 -54t-52 -145v-2q0 -89 53 -144.5t129 -55.5z" horiz-adv-x="664"/>
<glyph unicode="r" glyph-name="r" d="M86 0v517h77v-135q30 67 85 107t126 37v-83h-6q-89 0 -147 -62t-58 -175v-206h-77z" horiz-adv-x="405"/>
<glyph unicode="s" glyph-name="s" d="M257 -10q-56 0 -113.5 20.5t-100.5 55.5l39 55q88 -66 179 -66q47 0 77 22.5t30 59.5v2q0 35 -32.5 54t-96.5 37q-39 11 -62 20t-52 25.5t-43 41.5t-14 59v2q0 66 49.5 107t126.5 41q100 0 186 -57l-35 -58q-77 50 -153 50q-45 0 -72.5 -21t-27.5 -54v-2q0 -14 8 -26 t18 -20t31.5 -17t34.5 -13t41 -13q29 -9 48.5 -16t44 -19.5t39 -27t24.5 -36.5t10 -49v-2q0 -72 -52 -113.5t-132 -41.5z" horiz-adv-x="498"/>
<glyph unicode="t" glyph-name="t" d="M259 -9q-67 0 -106.5 35.5t-39.5 110.5v312h-72v68h72v156h77v-156h164v-68h-164v-302q0 -86 87 -86q38 0 75 18v-66q-42 -22 -93 -22z" horiz-adv-x="404"/>
<glyph unicode="u" glyph-name="u" d="M276 -11q-92 0 -145 56.5t-53 150.5v321h77v-302q0 -73 37.5 -114.5t105.5 -41.5q67 0 111 45t44 117v296h76v-517h-76v90q-63 -101 -177 -101z" horiz-adv-x="616"/>
<glyph unicode="v" glyph-name="v" d="M259 -4l-224 521h85l174 -429l175 429h83l-225 -521h-68z" horiz-adv-x="587"/>
<glyph unicode="w" glyph-name="w" d="M223 -4l-181 521h82l136 -415l139 417h64l140 -417l136 415h80l-182 -521h-67l-139 408l-140 -408h-68z" horiz-adv-x="861"/>
<glyph unicode="x" glyph-name="x" d="M39 0l203 263l-195 254h88l153 -200l153 200h86l-196 -252l204 -265h-89l-160 211l-161 -211h-86z" horiz-adv-x="574"/>
<glyph unicode="y" glyph-name="y" d="M166 -163q-56 0 -110 26l26 61q37 -19 81 -19q35 0 59 21.5t47 75.5l-235 515h85l188 -431l166 431h82l-217 -534q-33 -79 -73 -112.5t-99 -33.5z" horiz-adv-x="591"/>
<glyph unicode="z" glyph-name="z" d="M56 0v49l343 403h-331v65h436v-49l-344 -403h344v-65h-448z" horiz-adv-x="556"/>
<glyph unicode="{" glyph-name="braceleft" d="M416 -141q-127 23 -172 70t-45 132q0 7 0.5 37t0.5 39q0 61 -26.5 90.5t-91.5 29.5h-26v60h26q65 0 91.5 29.5t26.5 90.5q0 9 -0.5 39t-0.5 37q0 85 45 132t172 70l12 -51q-100 -26 -128 -57.5t-28 -103.5q0 -10 0.5 -37t0.5 -38q0 -60 -26.5 -92.5t-77.5 -48.5 q53 -17 78.5 -49t25.5 -92q0 -11 -0.5 -38t-0.5 -37q0 -72 28 -103.5t128 -57.5z" horiz-adv-x="483"/>
<glyph unicode="|" glyph-name="bar" d="M119 -128v926h65v-926h-65z" horiz-adv-x="303"/>
<glyph unicode="}" glyph-name="braceright" d="M67 -141l-13 51q101 26 129 57.5t28 103.5q0 10 -0.5 37t-0.5 38q0 60 26.5 92.5t77.5 48.5q-53 17 -78.5 49t-25.5 92q0 11 0.5 38t0.5 37q0 72 -28 103.5t-129 57.5l13 51q127 -23 172 -70t45 -132q0 -7 -0.5 -37t-0.5 -39q0 -61 26.5 -90.5t90.5 -29.5h26v-60h-26 q-64 0 -90.5 -29.5t-26.5 -90.5q0 -9 0.5 -39t0.5 -37q0 -85 -45 -132t-172 -70z" horiz-adv-x="483"/>
<glyph unicode="~" glyph-name="asciitilde" d="M120 237l-47 13q15 57 37 81.5t58 24.5q23 0 70.5 -20.5t63.5 -20.5q19 0 29.5 10.5t22.5 39.5l46 -13q-15 -57 -36.5 -81.5t-57.5 -24.5q-24 0 -71 20.5t-63 20.5q-19 0 -29.5 -10.5t-22.5 -39.5z" horiz-adv-x="473"/>
<glyph unicode=" " glyph-name="uni00A0" horiz-adv-x="300"/>
<glyph unicode="¡" glyph-name="exclamdown" d="M90 595v105h92v-105h-92zM91 0v57l23 445h43l24 -445v-57h-90z" horiz-adv-x="271"/>
<glyph unicode="¢" glyph-name="cent" d="M212 -6l22 106q-79 28 -127.5 97.5t-48.5 155.5v2q0 111 77 190.5t188 79.5h13l15 81h65l-18 -90q70 -19 132 -84l-50 -52q-51 52 -98 67l-77 -392q7 -1 21 -1q87 0 159 77l48 -45q-89 -101 -211 -101q-18 0 -27 1l-18 -92h-65zM250 169l75 387h-4q-78 0 -131.5 -58 t-53.5 -141v-2q0 -62 31 -112t83 -74z" horiz-adv-x="581"/>
<glyph unicode="£" glyph-name="sterling" d="M67 0v46l84 21v228h-84v69h84v108q0 115 63 178q60 60 158 60q131 0 214 -108l-61 -48q-33 41 -68 62t-86 21q-59 0 -98 -37q-43 -45 -43 -128v-108h279v-69h-279v-225h355v-70h-518z" horiz-adv-x="644"/>
<glyph unicode="¥" glyph-name="yen" d="M305 0v116h-205v62h205v88h-205v62h181l-254 372h94l223 -342l225 342h90l-255 -372h182v-62h-205v-88h205v-62h-205v-116h-76z" horiz-adv-x="686"/>
<glyph unicode="§" glyph-name="section" d="M328 -10q-136 0 -229 95l50 48q79 -77 181 -77q50 0 76.5 18.5t26.5 47.5v2q0 28 -36.5 49.5t-126.5 42.5q-114 27 -161 59t-47 83v2q0 45 36 74.5t96 39.5q-74 38 -74 101v2q0 60 50 96.5t134 36.5q136 0 229 -95l-50 -48q-79 77 -181 77q-51 0 -77 -18t-26 -48v-2 q0 -28 35.5 -49t127.5 -43q115 -27 161.5 -58.5t46.5 -83.5v-2q0 -44 -36 -74t-96 -40q74 -36 74 -101v-2q0 -60 -50 -96.5t-134 -36.5zM387 263q48 0 78.5 20.5t30.5 48.5v2q0 26 -31.5 47t-110.5 40q-65 16 -109 16q-48 0 -78.5 -20.5t-30.5 -48.5v-2q0 -26 31.5 -47 t110.5 -40q64 -16 109 -16z" horiz-adv-x="632"/>
<glyph unicode="¨" glyph-name="dieresis" d="M293 595v95h89v-95h-89zM118 595v95h89v-95h-89z" horiz-adv-x="500"/>
<glyph unicode="©" glyph-name="copyright" d="M414 -12q-151 0 -256 105.5t-105 254.5v2q0 149 106 255.5t257 106.5t256 -105.5t105 -254.5v-2q0 -149 -106 -255.5t-257 -106.5zM414 19q140 0 235 97t95 234v2q0 137 -94.5 233t-233.5 96q-140 0 -235 -97t-95 -234v-2q0 -137 94.5 -233t233.5 -96zM419 163 q-79 0 -132.5 56t-53.5 135v2q0 79 54 135t134 56q81 0 144 -60l-37 -40q-52 50 -108 50t-93.5 -41t-37.5 -99v-2q0 -58 38.5 -99.5t93.5 -41.5q57 0 110 50l36 -35q-64 -66 -148 -66z" horiz-adv-x="830"/>
<glyph unicode="ª" glyph-name="ordfeminine" d="M168 408q-43 0 -75 24t-32 66v2q0 44 34 68.5t89 24.5q40 0 80 -11v5q0 34 -20.5 52.5t-57.5 18.5t-86 -22l-15 42q57 25 106 25q59 0 93 -32q29 -29 29 -84v-173h-49v38q-36 -44 -96 -44zM177 450q36 0 61.5 19.5t25.5 50.5v24q-37 11 -76 11q-35 0 -55 -14.5t-20 -38.5 v-2q0 -23 18.5 -36.5t45.5 -13.5zM58 289v47h260v-47h-260z" horiz-adv-x="390"/>
<glyph unicode="«" glyph-name="guillemotleft" d="M433 41l-162 214v8l162 213l58 -33l-135 -183l135 -185zM210 41l-162 214v8l162 213l59 -33l-135 -183l135 -185z" horiz-adv-x="550"/>
<glyph unicode="®" glyph-name="registered" d="M202 360q-73 0 -123.5 51t-50.5 123v1q0 72 51 123.5t123 51.5q73 0 123.5 -51t50.5 -123v-1q0 -72 -51 -123.5t-123 -51.5zM202 379q65 0 109.5 46t44.5 110v1q0 64 -44.5 109.5t-109.5 45.5t-109.5 -46t-44.5 -110v-1q0 -64 44.5 -109.5t109.5 -45.5zM134 450v177h80 q30 0 49 -14.5t19 -41.5q0 -42 -41 -54l47 -67h-41l-42 61h-37v-61h-34zM168 539h43q36 0 36 29t-36 29h-43v-58z" horiz-adv-x="403"/>
<glyph unicode="¯" glyph-name="macron" d="M94 615v63h312v-63h-312z" horiz-adv-x="500"/>
<glyph unicode="°" glyph-name="degree" d="M229 400q-62 0 -109.5 46t-47.5 106v2q0 61 47.5 106.5t109.5 45.5t109.5 -45.5t47.5 -106.5v-2q0 -60 -47.5 -106t-109.5 -46zM229 452q42 0 70.5 30t28.5 70v2q0 40 -28.5 70t-70.5 30t-70.5 -30t-28.5 -70v-2q0 -40 28.5 -70t70.5 -30z" horiz-adv-x="458"/>
<glyph unicode="±" glyph-name="plusminus" d="M549 364h-200v-196h-73v196h-199v70h199v196h73v-196h200v-70zM549 0h-472v69h472v-69z" horiz-adv-x="625"/>
<glyph unicode="´" glyph-name="acute" d="M182 595l94 145l80 -37l-114 -108h-60z" horiz-adv-x="500"/>
<glyph unicode="¶" glyph-name="paragraph" d="M338 0v264h-11q-123 0 -201 57.5t-78 159.5v2q0 100 73 158.5t191 58.5h106v-700h-80z" horiz-adv-x="510"/>
<glyph unicode="·" glyph-name="periodcentered" d="M77 248v105h92v-105h-92z" horiz-adv-x="245"/>
<glyph unicode="¸" glyph-name="cedilla" d="M225 -155l-75 34l92 134h61z" horiz-adv-x="500"/>
<glyph unicode="º" glyph-name="ordmasculine" d="M194 407q-62 0 -104 43t-42 104v2q0 62 42.5 105.5t105.5 43.5q62 0 104 -43t42 -104v-2q0 -62 -42.5 -105.5t-105.5 -43.5zM51 289v47h288v-47h-288zM196 455q40 0 66 29t26 70v2q0 42 -27 71.5t-67 29.5t-66 -29t-26 -70v-2q0 -42 27 -71.5t67 -29.5z" horiz-adv-x="390"/>
<glyph unicode="»" glyph-name="guillemotright" d="M340 41l-59 33l135 184l-135 184l59 34l162 -213v-8zM117 41l-58 33l135 184l-135 184l58 34l162 -213v-8z" horiz-adv-x="550"/>
<glyph unicode="¿" glyph-name="questiondown" d="M246 595v105h92v-105h-92zM272 -9q-101 0 -162.5 55.5t-61.5 142.5v2q0 89 58 139.5t150 63.5l9 108h51l16 -162l-5 -5q-96 -2 -147.5 -39.5t-51.5 -101.5v-2q0 -55 40 -92.5t106 -37.5q101 0 181 94l51 -49q-96 -116 -234 -116z" horiz-adv-x="537"/>
<glyph unicode="À" glyph-name="Agrave" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357zM326 901l101 -129h-64l-115 93z"/>
<glyph unicode="Á" glyph-name="Aacute" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357zM363 772l100 129l79 -36l-115 -93h-64z"/>
<glyph unicode="Â" glyph-name="Acircumflex" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357zM249 770l110 118h72l110 -118h-72l-75 60l-75 -60h-70z"/>
<glyph unicode="Ã" glyph-name="Atilde" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357zM282 763l-49 14q15 53 37 77.5t57 24.5q23 0 71 -19.5t63 -19.5q19 0 29.5 10.5t22.5 36.5l49 -14q-15 -53 -37 -77t-57 -24q-23 0 -71 19.5t-63 19.5q-19 0 -29.5 -10.5t-22.5 -37.5z "/>
<glyph unicode="Ä" glyph-name="Adieresis" d="M39 0l319 705h74l319 -705h-85l-82 185h-381l-83 -185h-81zM234 256h319l-159 357zM442 772v90h93v-90h-93zM255 772v90h93v-90h-93z"/>
<glyph unicode="Å" glyph-name="Aring" d="M284 750q0 42 32.5 70.5t78.5 28.5t78.5 -28.5t32.5 -70.5q0 -59 -56 -86l301 -664h-85l-82 185h-381l-83 -185h-81l300 664q-55 27 -55 86zM234 256h319l-159 357zM328 750q0 -26 19 -43.5t48 -17.5t48 17.5t19 43.5t-19 43.5t-48 17.5t-48 -17.5t-19 -43.5z"/>
<glyph unicode="Æ" glyph-name="AE" d="M17 0l411 700h552v-72h-409v-239h364v-72h-364v-245h414v-72h-494v185h-282l-109 -185h-83zM249 256h242v374h-24z" horiz-adv-x="1045"/>
<glyph unicode="Ç" glyph-name="Ccedilla" d="M369 -155l-75 34l77 112q-133 17 -218 117.5t-85 239.5v2q0 151 101 256.5t253 105.5q89 0 152 -28t121 -83l-54 -58q-101 96 -220 96q-116 0 -193.5 -83t-77.5 -204v-2q0 -122 78 -205.5t193 -83.5q68 0 120 25t106 76l52 -51q-57 -58 -118.5 -88.5t-145.5 -33.5z" horiz-adv-x="738"/>
<glyph unicode="È" glyph-name="Egrave" d="M285 891l101 -129h-64l-115 93zM99 0v700h506v-72h-427v-239h382v-72h-382v-245h432v-72h-511z" horiz-adv-x="670"/>
<glyph unicode="É" glyph-name="Eacute" d="M321 762l100 129l79 -36l-115 -93h-64zM99 0v700h506v-72h-427v-239h382v-72h-382v-245h432v-72h-511z" horiz-adv-x="670"/>
<glyph unicode="Ê" glyph-name="Ecircumflex" d="M99 0v700h506v-72h-427v-239h382v-72h-382v-245h432v-72h-511zM207 762l110 118h72l110 -118h-72l-75 60l-75 -60h-70z" horiz-adv-x="670"/>
<glyph unicode="Ë" glyph-name="Edieresis" d="M401 762v90h93v-90h-93zM214 762v90h93v-90h-93zM99 0v700h506v-72h-427v-239h382v-72h-382v-245h432v-72h-511z" horiz-adv-x="670"/>
<glyph unicode="Ì" glyph-name="Igrave" d="M106 0v700h79v-700h-79zM77 891l101 -129h-64l-115 93z" horiz-adv-x="291"/>
<glyph unicode="Í" glyph-name="Iacute" d="M106 0v700h79v-700h-79zM114 762l100 129l79 -36l-115 -93h-64z" horiz-adv-x="291"/>
<glyph unicode="Î" glyph-name="Icircumflex" d="M106 0v700h79v-700h-79zM0 762l110 118h72l110 -118h-72l-75 60l-75 -60h-70z" horiz-adv-x="291"/>
<glyph unicode="Ï" glyph-name="Idieresis" d="M106 0v700h79v-700h-79zM194 762v90h93v-90h-93zM7 762v90h93v-90h-93z" horiz-adv-x="291"/>
<glyph unicode="Ð" glyph-name="Eth" d="M129 0v316h-75v72h75v312h243q163 0 267.5 -99.5t104.5 -248.5v-2q0 -150 -104.5 -250t-267.5 -100h-243zM208 73h164q130 0 210 78t80 197v2q0 119 -80 198t-210 79h-164v-239h210v-72h-210v-243z" horiz-adv-x="812"/>
<glyph unicode="Ñ" glyph-name="Ntilde" d="M282 750l-49 14q15 53 37 77.5t57 24.5q23 0 71 -19.5t63 -19.5q19 0 29.5 10.5t22.5 36.5l49 -14q-15 -53 -37 -77t-57 -24q-23 0 -71 19.5t-63 19.5q-19 0 -29.5 -10.5t-22.5 -37.5zM99 0v700h74l441 -561v561h77v-700h-63l-452 574v-574h-77z"/>
<glyph unicode="Ò" glyph-name="Ograve" d="M356 901l101 -129h-64l-115 93zM424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2 q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="Ó" glyph-name="Oacute" d="M393 772l100 129l79 -36l-115 -93h-64zM424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2 q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="Ô" glyph-name="Ocircumflex" d="M424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2q0 -121 78.5 -205t197.5 -84zM279 772l110 118 h72l110 -118h-72l-75 60l-75 -60h-70z" horiz-adv-x="850"/>
<glyph unicode="Õ" glyph-name="Otilde" d="M312 763l-49 14q15 53 37 77.5t57 24.5q23 0 71 -19.5t63 -19.5q19 0 29.5 10.5t22.5 36.5l49 -14q-15 -53 -37 -77t-57 -24q-23 0 -71 19.5t-63 19.5q-19 0 -29.5 -10.5t-22.5 -37.5zM424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5 t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204v-2q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="Ö" glyph-name="Odieresis" d="M472 772v90h93v-90h-93zM285 772v90h93v-90h-93zM424 -12q-156 0 -256 105.5t-100 254.5v2q0 149 101 255.5t257 106.5t256 -105.5t100 -254.5v-2q0 -149 -101 -255.5t-257 -106.5zM426 61q119 0 196.5 83t77.5 204v2q0 121 -78.5 205t-197.5 84t-196.5 -83t-77.5 -204 v-2q0 -121 78.5 -205t197.5 -84z" horiz-adv-x="850"/>
<glyph unicode="×" glyph-name="multiply" d="M486 127l-174 177l-174 -177l-50 50l176 174l-176 174l52 52l174 -177l174 177l50 -50l-177 -174l177 -174z" horiz-adv-x="625"/>
<glyph unicode="Ø" glyph-name="Oslash" d="M424 -12q-127 0 -219 73l-62 -66h-83l99 108q-91 102 -91 245v2q0 149 101 255.5t257 106.5q128 0 219 -74l62 67h83l-99 -108q91 -102 91 -245v-2q0 -149 -101 -255.5t-257 -106.5zM213 163l381 416q-72 61 -170 61q-118 0 -196 -83.5t-78 -204.5v-2q0 -107 63 -187z M426 60q118 0 196 83.5t78 204.5v2q0 107 -63 187l-381 -416q72 -61 170 -61z" horiz-adv-x="850"/>
<glyph unicode="Ù" glyph-name="Ugrave" d="M310 888l101 -129h-64l-115 93zM379 -11q-133 0 -212 79.5t-79 224.5v407h79v-402q0 -113 56.5 -174.5t157.5 -61.5q99 0 155 59t56 172v407h79v-401q0 -150 -79 -230t-213 -80z" horiz-adv-x="759"/>
<glyph unicode="Ú" glyph-name="Uacute" d="M347 759l100 129l79 -36l-115 -93h-64zM379 -11q-133 0 -212 79.5t-79 224.5v407h79v-402q0 -113 56.5 -174.5t157.5 -61.5q99 0 155 59t56 172v407h79v-401q0 -150 -79 -230t-213 -80z" horiz-adv-x="759"/>
<glyph unicode="Û" glyph-name="Ucircumflex" d="M379 -11q-133 0 -212 79.5t-79 224.5v407h79v-402q0 -113 56.5 -174.5t157.5 -61.5q99 0 155 59t56 172v407h79v-401q0 -150 -79 -230t-213 -80zM233 759l110 118h72l110 -118h-72l-75 60l-75 -60h-70z" horiz-adv-x="759"/>
<glyph unicode="Ü" glyph-name="Udieresis" d="M427 759v90h93v-90h-93zM240 759v90h93v-90h-93zM379 -11q-133 0 -212 79.5t-79 224.5v407h79v-402q0 -113 56.5 -174.5t157.5 -61.5q99 0 155 59t56 172v407h79v-401q0 -150 -79 -230t-213 -80z" horiz-adv-x="759"/>
<glyph unicode="Ý" glyph-name="Yacute" d="M318 0v277l-291 423h96l236 -350l238 350h92l-291 -422v-278h-80zM326 759l100 129l79 -36l-115 -93h-64z" horiz-adv-x="716"/>
<glyph unicode="Þ" glyph-name="Thorn" d="M99 0v700h79v-120h183q119 0 190.5 -60t71.5 -162v-2q0 -109 -78.5 -169.5t-196.5 -60.5h-170v-126h-79zM178 198h173q87 0 139.5 43t52.5 112v2q0 73 -50.5 112.5t-137.5 39.5h-177v-309z" horiz-adv-x="673"/>
<glyph unicode="ß" glyph-name="germandbls" d="M292 -2v62q90 2 140.5 36t50.5 96v2q0 58 -50.5 93t-140.5 44v57q148 55 148 162v2q0 52 -36.5 84.5t-94.5 32.5q-65 0 -105.5 -45.5t-40.5 -121.5v-502h-77v501q0 106 61.5 171t165.5 65q93 0 149.5 -51.5t56.5 -128.5v-2q0 -65 -39.5 -111t-99.5 -72q79 -18 130.5 -62 t51.5 -116v-2q0 -93 -72.5 -144.5t-197.5 -49.5z" horiz-adv-x="620"/>
<glyph unicode="à" glyph-name="agrave" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM261 595l-114 108l80 37l94 -145h-60z" horiz-adv-x="579"/>
<glyph unicode="á" glyph-name="aacute" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM258 595l94 145l80 -37l-114 -108h-60z" horiz-adv-x="579"/>
<glyph unicode="â" glyph-name="acircumflex" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM149 595l106 131h68l106 -131h-66l-75 67l-75 -67h-64z" horiz-adv-x="579"/>
<glyph unicode="ã" glyph-name="atilde" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM176 586l-47 13q15 56 37 80.5t58 24.5q24 0 71 -20t63 -20q19 0 29.5 10.5t22.5 38.5l47 -13q-15 -56 -37 -80.5t-58 -24.5q-24 0 -71 20t-63 20q-19 0 -29.5 -10.5t-22.5 -38.5z" horiz-adv-x="579"/>
<glyph unicode="ä" glyph-name="adieresis" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM332 595v95h89v-95h-89zM157 595v95h89v-95h-89z" horiz-adv-x="579"/>
<glyph unicode="å" glyph-name="aring" d="M240 -11q-78 0 -135.5 43t-57.5 118v2q0 81 60 125t161 44q80 0 158 -22v16q0 68 -40 104t-113 36q-76 0 -157 -37l-23 63q96 44 188 44q110 0 167 -57q54 -54 54 -152v-316h-76v77q-69 -88 -186 -88zM255 51q72 0 122 39t50 100v48q-76 22 -151 22q-71 0 -111 -28.5 t-40 -75.5v-2q0 -47 37.5 -75t92.5 -28zM180 698q0 43 32 73.5t77 30.5t77 -30.5t32 -73.5t-32 -74t-77 -31t-77 31t-32 74zM220 698q0 -28 20.5 -48.5t48.5 -20.5t48.5 20.5t20.5 48.5t-20.5 48.5t-48.5 20.5t-48.5 -20.5t-20.5 -48.5z" horiz-adv-x="579"/>
<glyph unicode="æ" glyph-name="ae" d="M236 -11q-75 0 -132 43t-57 118v2q0 81 59.5 125t157.5 44q67 0 153 -22v16q0 69 -39 104.5t-109 35.5q-72 0 -153 -37l-23 63q96 44 184 44q71 0 120.5 -28.5t68.5 -83.5q31 53 83.5 84t114.5 31q111 0 175.5 -78t64.5 -196q0 -15 -1 -27h-410q8 -79 59.5 -125.5 t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-62 0 -116 27.5t-89 76.5q-43 -45 -106.5 -74t-125.5 -29zM493 289h333q-7 75 -49.5 124.5t-115.5 49.5q-66 0 -113 -49t-55 -125zM251 51q62 0 103.5 23t82.5 69q-18 45 -21 97q-76 20 -144 20t-107.5 -28.5t-39.5 -75.5 v-2q0 -47 37.5 -75t88.5 -28z" horiz-adv-x="953"/>
<glyph unicode="ç" glyph-name="ccedilla" d="M268 -155l-75 34l78 114q-95 16 -156 91t-61 172v2q0 111 76.5 190.5t188.5 79.5q120 0 207 -92l-51 -54q-74 78 -157 78q-78 0 -131.5 -58t-53.5 -142v-2q0 -84 54.5 -142.5t135.5 -58.5q86 0 158 77l49 -46q-84 -94 -196 -99z" horiz-adv-x="571"/>
<glyph unicode="è" glyph-name="egrave" d="M132 227q8 -79 59.5 -125.5t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-109 0 -183.5 75.5t-74.5 194.5q0 113 71 191.5t177 78.5q111 0 176 -78t65 -196q0 -15 -1 -27h-410zM132 289h333q-7 75 -49.5 124.5t-115.5 49.5q-66 0 -113 -49t-55 -125zM272 595 l-114 108l80 37l94 -145h-60z" horiz-adv-x="592"/>
<glyph unicode="é" glyph-name="eacute" d="M270 595l94 145l80 -37l-114 -108h-60zM132 227q8 -79 59.5 -125.5t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-109 0 -183.5 75.5t-74.5 194.5q0 113 71 191.5t177 78.5q111 0 176 -78t65 -196q0 -15 -1 -27h-410zM132 289h333q-7 75 -49.5 124.5t-115.5 49.5 q-66 0 -113 -49t-55 -125z" horiz-adv-x="592"/>
<glyph unicode="ê" glyph-name="ecircumflex" d="M161 595l106 131h68l106 -131h-66l-75 67l-75 -67h-64zM132 227q8 -79 59.5 -125.5t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-109 0 -183.5 75.5t-74.5 194.5q0 113 71 191.5t177 78.5q111 0 176 -78t65 -196q0 -15 -1 -27h-410zM132 289h333 q-7 75 -49.5 124.5t-115.5 49.5q-66 0 -113 -49t-55 -125z" horiz-adv-x="592"/>
<glyph unicode="ë" glyph-name="edieresis" d="M344 595v95h89v-95h-89zM169 595v95h89v-95h-89zM132 227q8 -79 59.5 -125.5t122.5 -46.5q93 0 163 73l48 -43q-87 -97 -213 -97q-109 0 -183.5 75.5t-74.5 194.5q0 113 71 191.5t177 78.5q111 0 176 -78t65 -196q0 -15 -1 -27h-410zM132 289h333q-7 75 -49.5 124.5 t-115.5 49.5q-66 0 -113 -49t-55 -125z" horiz-adv-x="592"/>
<glyph unicode="ì" glyph-name="igrave" d="M93 0v517h77v-517h-77zM103 595l-114 108l80 37l94 -145h-60z" horiz-adv-x="263"/>
<glyph unicode="í" glyph-name="iacute" d="M101 595l94 145l80 -37l-114 -108h-60zM93 0v517h77v-517h-77z" horiz-adv-x="263"/>
<glyph unicode="î" glyph-name="icircumflex" d="M-8 595l106 131h68l106 -131h-66l-75 67l-75 -67h-64zM93 0v517h77v-517h-77z" horiz-adv-x="263"/>
<glyph unicode="ï" glyph-name="idieresis" d="M175 595v95h89v-95h-89zM0 595v95h89v-95h-89zM93 0v517h77v-517h-77z" horiz-adv-x="263"/>
<glyph unicode="ð" glyph-name="eth" d="M493 685l-86 -36q184 -179 184 -365v-2q0 -129 -75 -211.5t-197 -82.5q-112 0 -188.5 79.5t-76.5 192.5v2q0 110 72.5 182t171.5 72q100 0 179 -72q-42 85 -139 176l-115 -47l-23 47l95 39q-43 37 -89 71h111l48 -41l105 44zM324 57q82 0 135 57t53 142v2q0 83 -55 138 t-141 55q-82 0 -132.5 -55t-50.5 -138v-2q0 -83 55 -141t136 -58z" horiz-adv-x="646"/>
<glyph unicode="ñ" glyph-name="ntilde" d="M86 0v517h77v-90q61 101 177 101q92 0 145 -56.5t53 -150.5v-321h-77v302q0 73 -37.5 114.5t-105.5 41.5q-67 0 -111 -45t-44 -117v-296h-77zM198 586l-47 13q15 56 37 80.5t58 24.5q24 0 71 -20t63 -20q19 0 29.5 10.5t22.5 38.5l47 -13q-15 -56 -37 -80.5t-58 -24.5 q-24 0 -71 20t-63 20q-19 0 -29.5 -10.5t-22.5 -38.5z" horiz-adv-x="616"/>
<glyph unicode="ò" glyph-name="ograve" d="M322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5t-53.5 -141.5v-2q0 -84 55 -142.5t136 -58.5zM294 595 l-114 108l80 37l94 -145h-60z" horiz-adv-x="646"/>
<glyph unicode="ó" glyph-name="oacute" d="M292 595l94 145l80 -37l-114 -108h-60zM322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5t-53.5 -141.5v-2 q0 -84 55 -142.5t136 -58.5z" horiz-adv-x="646"/>
<glyph unicode="ô" glyph-name="ocircumflex" d="M183 595l106 131h68l106 -131h-66l-75 67l-75 -67h-64zM322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5 t-53.5 -141.5v-2q0 -84 55 -142.5t136 -58.5z" horiz-adv-x="646"/>
<glyph unicode="õ" glyph-name="otilde" d="M322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5t-53.5 -141.5v-2q0 -84 55 -142.5t136 -58.5zM209 586 l-47 13q15 56 37 80.5t58 24.5q24 0 71 -20t63 -20q19 0 29.5 10.5t22.5 38.5l47 -13q-15 -56 -37 -80.5t-58 -24.5q-24 0 -71 20t-63 20q-19 0 -29.5 -10.5t-22.5 -38.5z" horiz-adv-x="646"/>
<glyph unicode="ö" glyph-name="odieresis" d="M366 595v95h89v-95h-89zM191 595v95h89v-95h-89zM322 -12q-115 0 -191.5 78.5t-76.5 189.5v2q0 111 77.5 190.5t192.5 79.5t192 -78.5t77 -189.5v-2q0 -111 -78 -190.5t-193 -79.5zM324 57q83 0 136.5 58t53.5 141v2q0 84 -55.5 143t-136.5 59q-82 0 -135.5 -58.5 t-53.5 -141.5v-2q0 -84 55 -142.5t136 -58.5z" horiz-adv-x="646"/>
<glyph unicode="÷" glyph-name="divide" d="M262 499v104h101v-104h-101zM557 317h-489v70h489v-70zM262 102v103h101v-103h-101z" horiz-adv-x="625"/>
<glyph unicode="ø" glyph-name="oslash" d="M322 -12q-90 0 -161 53l-45 -46h-77l79 85q-64 75 -64 176v2q0 111 77.5 190.5t192.5 79.5q93 0 161 -54l45 49h78q-69 -73 -80 -86q65 -75 65 -177v-2q0 -111 -78 -190.5t-193 -79.5zM170 136l268 283q-52 42 -116 42q-83 0 -136.5 -58.5t-53.5 -142.5v-2q0 -67 38 -122 zM324 56q83 0 137 58.5t54 141.5v2q0 68 -39 122l-267 -283q49 -41 115 -41z" horiz-adv-x="646"/>
<glyph unicode="ù" glyph-name="ugrave" d="M276 -11q-92 0 -145 56.5t-53 150.5v321h77v-302q0 -73 37.5 -114.5t105.5 -41.5q67 0 111 45t44 117v296h76v-517h-76v90q-63 -101 -177 -101zM274 595l-114 108l80 37l94 -145h-60z" horiz-adv-x="616"/>
<glyph unicode="ú" glyph-name="uacute" d="M272 595l94 145l80 -37l-114 -108h-60zM276 -11q-92 0 -145 56.5t-53 150.5v321h77v-302q0 -73 37.5 -114.5t105.5 -41.5q67 0 111 45t44 117v296h76v-517h-76v90q-63 -101 -177 -101z" horiz-adv-x="616"/>
<glyph unicode="û" glyph-name="ucircumflex" d="M163 595l106 131h68l106 -131h-66l-75 67l-75 -67h-64zM276 -11q-92 0 -145 56.5t-53 150.5v321h77v-302q0 -73 37.5 -114.5t105.5 -41.5q67 0 111 45t44 117v296h76v-517h-76v90q-63 -101 -177 -101z" horiz-adv-x="616"/>
<glyph unicode="ü" glyph-name="udieresis" d="M346 595v95h89v-95h-89zM171 595v95h89v-95h-89zM276 -11q-92 0 -145 56.5t-53 150.5v321h77v-302q0 -73 37.5 -114.5t105.5 -41.5q67 0 111 45t44 117v296h76v-517h-76v90q-63 -101 -177 -101z" horiz-adv-x="616"/>
<glyph unicode="ý" glyph-name="yacute" d="M166 -163q-56 0 -110 26l26 61q37 -19 81 -19q35 0 59 21.5t47 75.5l-235 515h85l188 -431l166 431h82l-217 -534q-33 -79 -73 -112.5t-99 -33.5zM267 595l94 145l80 -37l-114 -108h-60z" horiz-adv-x="591"/>
<glyph unicode="þ" glyph-name="thorn" d="M86 -160v890h77v-317q78 115 198 115q99 0 173 -73t74 -195v-2q0 -121 -74 -195t-173 -74q-122 0 -198 110v-259h-77zM347 58q78 0 130 54t52 145v2q0 89 -53 144.5t-129 55.5q-75 0 -131 -56.5t-56 -142.5v-2q0 -87 56 -143.5t131 -56.5z" horiz-adv-x="664"/>
<hkern g2="V" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g2="X" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="5"/>
<hkern g2="asterisk" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g2="backslash" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="120"/>
<hkern g2="question" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="60"/>
<hkern g2="v" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="63"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="90"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="90"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="110"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="48"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="43"/>
<hkern g2="AE,AEacute" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="5"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="5"/>
<hkern g2="quoteright,quotedblright" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="80"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="25"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="25"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="41"/>
<hkern g2="guillemotleft,guilsinglleft" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="30"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="10"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="14"/>
<hkern g2="hyphen,endash,emdash" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="40"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="25"/>
<hkern g2="quoteleft,quotedblleft" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="80"/>
<hkern g2="registered,servicemark,trademark" g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" k="100"/>
<hkern g2="V" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="1"/>
<hkern g2="X" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="v" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="x" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="1"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="20"/>
<hkern g2="hyphen,endash,emdash" g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron" k="10"/>
<hkern g2="V" g1="D,Eth,Dcaron,Dcroat" k="45"/>
<hkern g2="X" g1="D,Eth,Dcaron,Dcroat" k="55"/>
<hkern g2="backslash" g1="D,Eth,Dcaron,Dcroat" k="40"/>
<hkern g2="question" g1="D,Eth,Dcaron,Dcroat" k="20"/>
<hkern g2="x" g1="D,Eth,Dcaron,Dcroat" k="10"/>
<hkern g2="J" g1="D,Eth,Dcaron,Dcroat" k="40"/>
<hkern g2="braceright" g1="D,Eth,Dcaron,Dcroat" k="20"/>
<hkern g2="bracketright" g1="D,Eth,Dcaron,Dcroat" k="20"/>
<hkern g2="parenright" g1="D,Eth,Dcaron,Dcroat" k="30"/>
<hkern g2="slash" g1="D,Eth,Dcaron,Dcroat" k="40"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="D,Eth,Dcaron,Dcroat" k="51"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="D,Eth,Dcaron,Dcroat" k="36"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="D,Eth,Dcaron,Dcroat" k="65"/>
<hkern g2="AE,AEacute" g1="D,Eth,Dcaron,Dcroat" k="46"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="D,Eth,Dcaron,Dcroat" k="46"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="D,Eth,Dcaron,Dcroat" k="44"/>
<hkern g2="comma,period,ellipsis" g1="D,Eth,Dcaron,Dcroat" k="40"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="D,Eth,Dcaron,Dcroat" k="10"/>
<hkern g2="v" g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" k="10"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" k="10"/>
<hkern g2="V" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="21"/>
<hkern g2="X" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="10"/>
<hkern g2="backslash" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="15"/>
<hkern g2="question" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="10"/>
<hkern g2="v" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="5"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="17"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="16"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="30"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="5"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="G,Gbreve,Gdotaccent,Gcommaaccent" k="-10"/>
<hkern g2="V" g1="K,Kcommaaccent" k="30"/>
<hkern g2="v" g1="K,Kcommaaccent" k="60"/>
<hkern g2="ampersand" g1="K,Kcommaaccent" k="7"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="K,Kcommaaccent" k="10"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="K,Kcommaaccent" k="30"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="K,Kcommaaccent" k="37"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="K,Kcommaaccent" k="50"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="K,Kcommaaccent" k="50"/>
<hkern g2="AE,AEacute" g1="K,Kcommaaccent" k="5"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="K,Kcommaaccent" k="5"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="K,Kcommaaccent" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="K,Kcommaaccent" k="25"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="K,Kcommaaccent" k="30"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="K,Kcommaaccent" k="51"/>
<hkern g2="guillemotleft,guilsinglleft" g1="K,Kcommaaccent" k="20"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="K,Kcommaaccent" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="K,Kcommaaccent" k="25"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="K,Kcommaaccent" k="20"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="K,Kcommaaccent" k="10"/>
<hkern g2="hyphen,endash,emdash" g1="K,Kcommaaccent" k="50"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="K,Kcommaaccent" k="15"/>
<hkern g2="V" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="115"/>
<hkern g2="asterisk" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="80"/>
<hkern g2="backslash" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="120"/>
<hkern g2="question" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="60"/>
<hkern g2="v" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="60"/>
<hkern g2="ampersand" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="7"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="100"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="100"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="130"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="50"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="60"/>
<hkern g2="quoteright,quotedblright" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="40"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="5"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="10"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="40"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="20"/>
<hkern g2="hyphen,endash,emdash" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="40"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="20"/>
<hkern g2="quoteleft,quotedblleft" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="40"/>
<hkern g2="registered,servicemark,trademark" g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" k="90"/>
<hkern g2="V" g1="R,Racute,Rcommaaccent,Rcaron" k="20"/>
<hkern g2="J" g1="R,Racute,Rcommaaccent,Rcaron" k="5"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="R,Racute,Rcommaaccent,Rcaron" k="7"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="R,Racute,Rcommaaccent,Rcaron" k="15"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="R,Racute,Rcommaaccent,Rcaron" k="25"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="R,Racute,Rcommaaccent,Rcaron" k="5"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="R,Racute,Rcommaaccent,Rcaron" k="10"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="R,Racute,Rcommaaccent,Rcaron" k="-10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="R,Racute,Rcommaaccent,Rcaron" k="-10"/>
<hkern g2="V" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="30"/>
<hkern g2="X" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="25"/>
<hkern g2="backslash" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="20"/>
<hkern g2="question" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g2="v" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="x" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="25"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="AE,AEacute" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="15"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g2="t,tcommaaccent,tcaron" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="S,Sacute,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g2="v" g1="T,Tcommaaccent,Tcaron" k="86"/>
<hkern g2="x" g1="T,Tcommaaccent,Tcaron" k="86"/>
<hkern g2="J" g1="T,Tcommaaccent,Tcaron" k="110"/>
<hkern g2="slash" g1="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g2="ampersand" g1="T,Tcommaaccent,Tcaron" k="63"/>
<hkern g2="j" g1="T,Tcommaaccent,Tcaron" k="32"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="T,Tcommaaccent,Tcaron" k="84"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="T,Tcommaaccent,Tcaron" k="86"/>
<hkern g2="AE,AEacute" g1="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="T,Tcommaaccent,Tcaron" k="17"/>
<hkern g2="guillemotright,guilsinglright" g1="T,Tcommaaccent,Tcaron" k="70"/>
<hkern g2="comma,period,ellipsis" g1="T,Tcommaaccent,Tcaron" k="100"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="T,Tcommaaccent,Tcaron" k="127"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="T,Tcommaaccent,Tcaron" k="117"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="T,Tcommaaccent,Tcaron" k="127"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="T,Tcommaaccent,Tcaron" k="48"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="T,Tcommaaccent,Tcaron" k="108"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="T,Tcommaaccent,Tcaron" k="102"/>
<hkern g2="guillemotleft,guilsinglleft" g1="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="T,Tcommaaccent,Tcaron" k="45"/>
<hkern g2="t,tcommaaccent,tcaron" g1="T,Tcommaaccent,Tcaron" k="42"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="T,Tcommaaccent,Tcaron" k="86"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="T,Tcommaaccent,Tcaron" k="15"/>
<hkern g2="colon,semicolon" g1="T,Tcommaaccent,Tcaron" k="39"/>
<hkern g2="hyphen,endash,emdash" g1="T,Tcommaaccent,Tcaron" k="90"/>
<hkern g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" g1="T,Tcommaaccent,Tcaron" k="16"/>
<hkern g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" g1="T,Tcommaaccent,Tcaron" k="89"/>
<hkern g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek" g1="T,Tcommaaccent,Tcaron" k="32"/>
<hkern g2="X" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="10"/>
<hkern g2="x" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="5"/>
<hkern g2="J" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="20"/>
<hkern g2="slash" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g2="AE,AEacute" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="25"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="25"/>
<hkern g2="comma,period,ellipsis" g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g2="V" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g2="X" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g2="v" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="x" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="40"/>
<hkern g2="J" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="105"/>
<hkern g2="slash" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="100"/>
<hkern g2="ampersand" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="41"/>
<hkern g2="j" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="AE,AEacute" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="90"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="90"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="comma,period,ellipsis" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="100"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="70"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="60"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="65"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="36"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="60"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="55"/>
<hkern g2="guillemotleft,guilsinglleft" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="50"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="30"/>
<hkern g2="t,tcommaaccent,tcaron" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="25"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="20"/>
<hkern g2="colon,semicolon" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g2="hyphen,endash,emdash" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="10"/>
<hkern g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="35"/>
<hkern g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek" g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" k="15"/>
<hkern g2="V" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g2="X" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="23"/>
<hkern g2="v" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g2="x" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="70"/>
<hkern g2="J" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="130"/>
<hkern g2="slash" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="110"/>
<hkern g2="ampersand" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="66"/>
<hkern g2="j" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="5"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="55"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g2="AE,AEacute" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="110"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="110"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="75"/>
<hkern g2="comma,period,ellipsis" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="130"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="100"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="105"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="110"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="60"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="100"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="80"/>
<hkern g2="guillemotleft,guilsinglleft" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="100"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="40"/>
<hkern g2="t,tcommaaccent,tcaron" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="30"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="75"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="35"/>
<hkern g2="colon,semicolon" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="40"/>
<hkern g2="hyphen,endash,emdash" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="80"/>
<hkern g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="10"/>
<hkern g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="75"/>
<hkern g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek" g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" k="20"/>
<hkern g2="v" g1="Z,Zacute,Zdotaccent,Zcaron" k="20"/>
<hkern g2="ampersand" g1="Z,Zacute,Zdotaccent,Zcaron" k="11"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="Z,Zacute,Zdotaccent,Zcaron" k="15"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="Z,Zacute,Zdotaccent,Zcaron" k="15"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="Z,Zacute,Zdotaccent,Zcaron" k="20"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="Z,Zacute,Zdotaccent,Zcaron" k="25"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="Z,Zacute,Zdotaccent,Zcaron" k="39"/>
<hkern g2="guillemotleft,guilsinglleft" g1="Z,Zacute,Zdotaccent,Zcaron" k="20"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="Z,Zacute,Zdotaccent,Zcaron" k="10"/>
<hkern g2="hyphen,endash,emdash" g1="Z,Zacute,Zdotaccent,Zcaron" k="30"/>
<hkern g2="V" g1="colon,semicolon" k="20"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="colon,semicolon" k="39"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="colon,semicolon" k="15"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="colon,semicolon" k="40"/>
<hkern g2="V" g1="hyphen,endash,emdash" k="40"/>
<hkern g2="X" g1="hyphen,endash,emdash" k="50"/>
<hkern g2="v" g1="hyphen,endash,emdash" k="15"/>
<hkern g2="x" g1="hyphen,endash,emdash" k="30"/>
<hkern g2="one" g1="hyphen,endash,emdash" k="30"/>
<hkern g2="seven" g1="hyphen,endash,emdash" k="40"/>
<hkern g2="three" g1="hyphen,endash,emdash" k="10"/>
<hkern g2="three.alt" g1="hyphen,endash,emdash" k="10"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="hyphen,endash,emdash" k="90"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="hyphen,endash,emdash" k="35"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="hyphen,endash,emdash" k="80"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="hyphen,endash,emdash" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="hyphen,endash,emdash" k="15"/>
<hkern g2="AE,AEacute" g1="hyphen,endash,emdash" k="40"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="hyphen,endash,emdash" k="40"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="hyphen,endash,emdash" k="30"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="hyphen,endash,emdash" k="10"/>
<hkern g2="V" g1="guillemotleft,guilsinglleft" k="40"/>
<hkern g2="X" g1="guillemotleft,guilsinglleft" k="20"/>
<hkern g2="v" g1="guillemotleft,guilsinglleft" k="15"/>
<hkern g2="x" g1="guillemotleft,guilsinglleft" k="15"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="guillemotleft,guilsinglleft" k="70"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="guillemotleft,guilsinglleft" k="35"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="guillemotleft,guilsinglleft" k="75"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="guillemotleft,guilsinglleft" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="guillemotleft,guilsinglleft" k="15"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="guillemotleft,guilsinglleft" k="5"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="guillemotleft,guilsinglleft" k="10"/>
<hkern g2="V" g1="guillemotright,guilsinglright" k="60"/>
<hkern g2="X" g1="guillemotright,guilsinglright" k="50"/>
<hkern g2="v" g1="guillemotright,guilsinglright" k="30"/>
<hkern g2="x" g1="guillemotright,guilsinglright" k="45"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="guillemotright,guilsinglright" k="90"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="guillemotright,guilsinglright" k="50"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="guillemotright,guilsinglright" k="100"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="guillemotright,guilsinglright" k="20"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="guillemotright,guilsinglright" k="30"/>
<hkern g2="AE,AEacute" g1="guillemotright,guilsinglright" k="20"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="guillemotright,guilsinglright" k="20"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="guillemotright,guilsinglright" k="15"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="guillemotright,guilsinglright" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="guillemotright,guilsinglright" k="20"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="guillemotright,guilsinglright" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="guillemotright,guilsinglright" k="10"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="guillemotright,guilsinglright" k="10"/>
<hkern g2="V" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="120"/>
<hkern g2="v" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="85"/>
<hkern g2="j" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="-15"/>
<hkern g2="one" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="50"/>
<hkern g2="seven" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="20"/>
<hkern g2="zero" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="20"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="100"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="100"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="130"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="70"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="60"/>
<hkern g2="quoteright,quotedblright" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="40"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="20"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="40"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="15"/>
<hkern g2="t,tcommaaccent,tcaron" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="25"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="comma,period,quotesinglbase,quotedblbase,ellipsis" k="15"/>
<hkern g2="J" g1="quoteright,quotedblright" k="100"/>
<hkern g2="AE,AEacute" g1="quoteright,quotedblright" k="100"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="quoteright,quotedblright" k="100"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="quoteright,quotedblright" k="20"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="quoteright,quotedblright" k="37"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="quoteright,quotedblright" k="42"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="quoteright,quotedblright" k="26"/>
<hkern g2="J" g1="quoteleft,quotedblleft" k="80"/>
<hkern g2="questiondown" g1="quoteleft,quotedblleft" k="35"/>
<hkern g2="AE,AEacute" g1="quoteleft,quotedblleft" k="90"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="quoteleft,quotedblleft" k="90"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="quoteleft,quotedblleft" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="quoteleft,quotedblleft" k="15"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="quoteleft,quotedblleft" k="15"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="quoteleft,quotedblleft" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="quoteleft,quotedblleft" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="quoteleft,quotedblleft" k="-15"/>
<hkern g2="asterisk" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="15"/>
<hkern g2="backslash" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="75"/>
<hkern g2="question" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="35"/>
<hkern g2="v" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="20"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2" k="5"/>
<hkern g2="asterisk" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="15"/>
<hkern g2="backslash" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="75"/>
<hkern g2="question" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="30"/>
<hkern g2="v" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="15"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron" k="15"/>
<hkern g2="asterisk" g1="b,p,thorn" k="15"/>
<hkern g2="backslash" g1="b,p,thorn" k="70"/>
<hkern g2="question" g1="b,p,thorn" k="35"/>
<hkern g2="v" g1="b,p,thorn" k="25"/>
<hkern g2="x" g1="b,p,thorn" k="30"/>
<hkern g2="braceright" g1="b,p,thorn" k="15"/>
<hkern g2="bracketright" g1="b,p,thorn" k="20"/>
<hkern g2="parenright" g1="b,p,thorn" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="b,p,thorn" k="20"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="b,p,thorn" k="25"/>
<hkern g2="guillemotright,guilsinglright" g1="b,p,thorn" k="5"/>
<hkern g2="comma,period,ellipsis" g1="b,p,thorn" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="b,p,thorn" k="15"/>
<hkern g2="quoteleft,quotedblleft" g1="b,p,thorn" k="10"/>
<hkern g2="backslash" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="40"/>
<hkern g2="question" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="15"/>
<hkern g2="v" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="5"/>
<hkern g2="x" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="10"/>
<hkern g2="parenright" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="15"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="5"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="5"/>
<hkern g2="guillemotright,guilsinglright" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="-10"/>
<hkern g2="quoteright,quotedblright" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="-15"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="15"/>
<hkern g2="guillemotleft,guilsinglleft" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="10"/>
<hkern g2="quoteleft,quotedblleft" g1="c,ccedilla,cacute,cdotaccent,ccaron" k="-10"/>
<hkern g2="asterisk" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="20"/>
<hkern g2="backslash" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="80"/>
<hkern g2="question" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="40"/>
<hkern g2="v" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="25"/>
<hkern g2="x" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="30"/>
<hkern g2="braceright" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="10"/>
<hkern g2="bracketright" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="20"/>
<hkern g2="parenright" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="25"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="25"/>
<hkern g2="comma,period,ellipsis" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,schwa" k="15"/>
<hkern g2="asterisk" g1="f,f_f" k="-30"/>
<hkern g2="backslash" g1="f,f_f" k="-30"/>
<hkern g2="question" g1="f,f_f" k="-35"/>
<hkern g2="braceright" g1="f,f_f" k="-30"/>
<hkern g2="bracketright" g1="f,f_f" k="-20"/>
<hkern g2="parenright" g1="f,f_f" k="-30"/>
<hkern g2="slash" g1="f,f_f" k="45"/>
<hkern g2="comma,period,ellipsis" g1="f,f_f" k="45"/>
<hkern g2="quoteright,quotedblright" g1="f,f_f" k="-35"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="f,f_f" k="15"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="f,f_f" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="f,f_f" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="f,f_f" k="10"/>
<hkern g2="guillemotleft,guilsinglleft" g1="f,f_f" k="15"/>
<hkern g2="quoteleft,quotedblleft" g1="f,f_f" k="-30"/>
<hkern g2="registered,servicemark,trademark" g1="f,f_f" k="-51"/>
<hkern g2="backslash" g1="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" k="50"/>
<hkern g2="backslash" g1="g,gbreve,gdotaccent,gcommaaccent" k="50"/>
<hkern g2="backslash" g1="k,kcommaaccent" k="40"/>
<hkern g2="v" g1="k,kcommaaccent" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="k,kcommaaccent" k="20"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="k,kcommaaccent" k="15"/>
<hkern g2="guillemotright,guilsinglright" g1="k,kcommaaccent" k="10"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="k,kcommaaccent" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="k,kcommaaccent" k="25"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="k,kcommaaccent" k="25"/>
<hkern g2="guillemotleft,guilsinglleft" g1="k,kcommaaccent" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="k,kcommaaccent" k="10"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="k,kcommaaccent" k="10"/>
<hkern g2="hyphen,endash,emdash" g1="k,kcommaaccent" k="20"/>
<hkern g2="asterisk" g1="r,racute,rcommaaccent,rcaron" k="-20"/>
<hkern g2="backslash" g1="r,racute,rcommaaccent,rcaron" k="30"/>
<hkern g2="slash" g1="r,racute,rcommaaccent,rcaron" k="75"/>
<hkern g2="comma,period,ellipsis" g1="r,racute,rcommaaccent,rcaron" k="90"/>
<hkern g2="quoteright,quotedblright" g1="r,racute,rcommaaccent,rcaron" k="-35"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="r,racute,rcommaaccent,rcaron" k="25"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="r,racute,rcommaaccent,rcaron" k="21"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="r,racute,rcommaaccent,rcaron" k="22"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="r,racute,rcommaaccent,rcaron" k="10"/>
<hkern g2="guillemotleft,guilsinglleft" g1="r,racute,rcommaaccent,rcaron" k="10"/>
<hkern g2="quoteleft,quotedblleft" g1="r,racute,rcommaaccent,rcaron" k="-20"/>
<hkern g2="asterisk" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="20"/>
<hkern g2="backslash" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="80"/>
<hkern g2="question" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="50"/>
<hkern g2="v" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="30"/>
<hkern g2="x" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="35"/>
<hkern g2="braceright" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="15"/>
<hkern g2="bracketright" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="20"/>
<hkern g2="parenright" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="25"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="30"/>
<hkern g2="guillemotright,guilsinglright" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="10"/>
<hkern g2="comma,period,ellipsis" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="20"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="20"/>
<hkern g2="quoteleft,quotedblleft" g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" k="20"/>
<hkern g2="V" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="41"/>
<hkern g2="X" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="50"/>
<hkern g2="backslash" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="40"/>
<hkern g2="question" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="20"/>
<hkern g2="x" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="5"/>
<hkern g2="J" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="30"/>
<hkern g2="braceright" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="20"/>
<hkern g2="bracketright" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="20"/>
<hkern g2="parenright" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="30"/>
<hkern g2="slash" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="40"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="48"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="36"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="60"/>
<hkern g2="AE,AEacute" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="41"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="41"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="39"/>
<hkern g2="comma,period,ellipsis" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="40"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" k="5"/>
<hkern g2="backslash" g1="s,sacute,scedilla,scaron,scommaaccent" k="75"/>
<hkern g2="question" g1="s,sacute,scedilla,scaron,scommaaccent" k="35"/>
<hkern g2="v" g1="s,sacute,scedilla,scaron,scommaaccent" k="20"/>
<hkern g2="x" g1="s,sacute,scedilla,scaron,scommaaccent" k="25"/>
<hkern g2="braceright" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="bracketright" g1="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g2="parenright" g1="s,sacute,scedilla,scaron,scommaaccent" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="s,sacute,scedilla,scaron,scommaaccent" k="15"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="guillemotleft,guilsinglleft" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="quoteleft,quotedblleft" g1="s,sacute,scedilla,scaron,scommaaccent" k="10"/>
<hkern g2="backslash" g1="t,tcommaaccent,tcaron" k="40"/>
<hkern g2="quoteright,quotedblright" g1="t,tcommaaccent,tcaron" k="-10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="t,tcommaaccent,tcaron" k="15"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="t,tcommaaccent,tcaron" k="15"/>
<hkern g2="guillemotleft,guilsinglleft" g1="t,tcommaaccent,tcaron" k="10"/>
<hkern g2="backslash" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="50"/>
<hkern g2="question" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="v" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g2="x" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="braceright" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="bracketright" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g2="slash" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="60"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="comma,period,ellipsis" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="70"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="25"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="15"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="5"/>
<hkern g2="guillemotleft,guilsinglleft" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="20"/>
<hkern g2="hyphen,endash,emdash" g1="w,wcircumflex,wgrave,wacute,wdieresis" k="10"/>
<hkern g2="backslash" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="50"/>
<hkern g2="question" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g2="v" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g2="x" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g2="braceright" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g2="bracketright" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="20"/>
<hkern g2="slash" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="70"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g2="comma,period,ellipsis" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="85"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="25"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="25"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="30"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="20"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="5"/>
<hkern g2="guillemotleft,guilsinglleft" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="30"/>
<hkern g2="hyphen,endash,emdash" g1="y,yacute,ydieresis,ycircumflex,ygrave" k="15"/>
<hkern g2="backslash" g1="z,zacute,zdotaccent,zcaron" k="45"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g2="guillemotleft,guilsinglleft" g1="z,zacute,zdotaccent,zcaron" k="15"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="B" k="17"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="B" k="15"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="B" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="B" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="B" k="10"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="F" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="F" k="15"/>
<hkern g2="AE,AEacute" g1="F" k="80"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="F" k="80"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="F" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="F" k="15"/>
<hkern g2="comma,period,ellipsis" g1="F" k="100"/>
<hkern g2="quoteright,quotedblright" g1="F" k="-20"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="F" k="25"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="F" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="F" k="15"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="F" k="15"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="F" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="F" k="15"/>
<hkern g2="AE,AEacute" g1="J" k="25"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="J" k="25"/>
<hkern g2="comma,period,ellipsis" g1="J" k="15"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="P" k="5"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="P" k="10"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="P" k="-10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="P" k="-10"/>
<hkern g2="AE,AEacute" g1="P" k="70"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="P" k="70"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="P" k="15"/>
<hkern g2="comma,period,ellipsis" g1="P" k="100"/>
<hkern g2="quoteright,quotedblright" g1="P" k="-20"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="P" k="10"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="P" k="5"/>
<hkern g2="guillemotleft,guilsinglleft" g1="P" k="-10"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="P" k="-15"/>
<hkern g2="t,tcommaaccent,tcaron" g1="P" k="-15"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="P" k="-5"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="Q" k="48"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="Q" k="36"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="Q" k="65"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="Q.alt" k="48"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="Q.alt" k="36"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="Q.alt" k="60"/>
<hkern g2="AE,AEacute" g1="Q.alt" k="41"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="Q.alt" k="41"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="Q.alt" k="39"/>
<hkern g2="comma,period,ellipsis" g1="Q.alt" k="40"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="Q.alt" k="5"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="V" k="10"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="V" k="20"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="V" k="35"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="V" k="40"/>
<hkern g2="AE,AEacute" g1="V" k="100"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="V" k="100"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="V" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="V" k="40"/>
<hkern g2="comma,period,ellipsis" g1="V" k="120"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="V" k="70"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="V" k="65"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="V" k="70"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="V" k="41"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="V" k="60"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="V" k="55"/>
<hkern g2="guillemotleft,guilsinglleft" g1="V" k="60"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="V" k="25"/>
<hkern g2="t,tcommaaccent,tcaron" g1="V" k="20"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="V" k="40"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="V" k="25"/>
<hkern g2="colon,semicolon" g1="V" k="20"/>
<hkern g2="hyphen,endash,emdash" g1="V" k="40"/>
<hkern g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" g1="V" k="10"/>
<hkern g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" g1="V" k="40"/>
<hkern g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek" g1="V" k="20"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="X" k="15"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="X" k="23"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="X" k="40"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="X" k="40"/>
<hkern g2="AE,AEacute" g1="X" k="5"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="X" k="5"/>
<hkern g2="guillemotright,guilsinglright" g1="X" k="20"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="X" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="X" k="40"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="X" k="45"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="X" k="50"/>
<hkern g2="guillemotleft,guilsinglleft" g1="X" k="50"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="X" k="20"/>
<hkern g2="t,tcommaaccent,tcaron" g1="X" k="20"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="X" k="20"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="X" k="30"/>
<hkern g2="hyphen,endash,emdash" g1="X" k="50"/>
<hkern g2="b,h,k,l,kcommaaccent,lacute,lcommaaccent,lcaron,ldot" g1="X" k="10"/>
<hkern g2="i,igrave,iacute,icircumflex,idieresis,imacron,ibreve,iogonek" g1="X" k="10"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="X" k="10"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="ampersand" k="79"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="ampersand" k="49"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="ampersand" k="67"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="ampersand" k="7"/>
<hkern g2="AE,AEacute" g1="asterisk" k="100"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="asterisk" k="100"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="asterisk" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="asterisk" k="15"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="asterisk" k="20"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="asterisk" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="asterisk" k="-10"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="backslash" k="90"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="backslash" k="100"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="backslash" k="110"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="backslash" k="60"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="backslash" k="60"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="backslash" k="40"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="backslash" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="backslash" k="30"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="backslash" k="15"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="braceleft" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="braceleft" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="braceleft" k="15"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="braceleft" k="15"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="braceleft" k="20"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="braceleft" k="10"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="braceleft" k="10"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="bracketleft" k="20"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="bracketleft" k="10"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="bracketleft" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="bracketleft" k="20"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="bracketleft" k="20"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="bracketleft" k="20"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="bracketleft" k="15"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="germandbls" k="5"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="germandbls" k="10"/>
<hkern g2="comma,period,ellipsis" g1="nine" k="10"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="parenleft" k="30"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="parenleft" k="30"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="parenleft" k="30"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="parenleft" k="15"/>
<hkern g2="T,Tcommaaccent,Tcaron" g1="questiondown" k="60"/>
<hkern g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" g1="questiondown" k="50"/>
<hkern g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" g1="questiondown" k="70"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="questiondown" k="35"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="questiondown" k="35"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="questiondown" k="-10"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="questiondown" k="20"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="questiondown" k="10"/>
<hkern g2="t,tcommaaccent,tcaron" g1="questiondown" k="15"/>
<hkern g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g1="questiondown" k="15"/>
<hkern g2="comma,period,ellipsis" g1="seven" k="100"/>
<hkern g2="hyphen,endash,emdash" g1="seven" k="30"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="slash" k="50"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="slash" k="50"/>
<hkern g2="AE,AEacute" g1="slash" k="120"/>
<hkern g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute" g1="slash" k="120"/>
<hkern g2="Z,Zacute,Zdotaccent,Zcaron" g1="slash" k="20"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="slash" k="65"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="slash" k="70"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="slash" k="80"/>
<hkern g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt" g1="slash" k="40"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="slash" k="85"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="slash" k="60"/>
<hkern g2="f,f_f,fi,fl,f_f_i,f_f_l" g1="slash" k="25"/>
<hkern g2="t,tcommaaccent,tcaron" g1="slash" k="20"/>
<hkern g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek" g1="slash" k="50"/>
<hkern g2="S,Sacute,Scedilla,Scaron,Scommaaccent" g1="slash" k="30"/>
<hkern g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron" g1="slash" k="50"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="v" k="15"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="v" k="15"/>
<hkern g2="guillemotright,guilsinglright" g1="v" k="15"/>
<hkern g2="comma,period,ellipsis" g1="v" k="85"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="v" k="25"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="v" k="25"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="v" k="30"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="v" k="20"/>
<hkern g2="z,zacute,zdotaccent,zcaron" g1="v" k="5"/>
<hkern g2="guillemotleft,guilsinglleft" g1="v" k="30"/>
<hkern g2="hyphen,endash,emdash" g1="v" k="15"/>
<hkern g2="w,wcircumflex,wgrave,wacute,wdieresis" g1="x" k="10"/>
<hkern g2="y,yacute,ydieresis,ycircumflex,ygrave" g1="x" k="10"/>
<hkern g2="guillemotright,guilsinglright" g1="x" k="15"/>
<hkern g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2" g1="x" k="15"/>
<hkern g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt" g1="x" k="30"/>
<hkern g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" g1="x" k="35"/>
<hkern g2="s,sacute,scedilla,scaron,scommaaccent" g1="x" k="20"/>
<hkern g2="guillemotleft,guilsinglleft" g1="x" k="45"/>
<hkern g2="hyphen,endash,emdash" g1="x" k="30"/>
<hkern g2="comma,period,ellipsis" g1="zero" k="20"/>
</font>
</defs>
</svg>
