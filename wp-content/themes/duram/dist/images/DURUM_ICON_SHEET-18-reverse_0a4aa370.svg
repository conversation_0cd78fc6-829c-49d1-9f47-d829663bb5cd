<svg width="580" height="400" xmlns="http://www.w3.org/2000/svg">
 <!-- Created with Method Draw - http://github.com/duopixel/Method-Draw/ -->

 <g>
  <title>background</title>
  <rect fill="#fff" id="canvas_background" height="402" width="582" y="-1" x="-1"/>
  <g display="none" overflow="visible" y="0" x="0" height="100%" width="100%" id="canvasGrid">
   <rect fill="url(#gridpattern)" stroke-width="0" y="0" x="0" height="100%" width="100%"/>
  </g>
 </g>
 <g>
  <title>Layer 1</title>
  <defs>
   <style>.cls-1,.cls-3{fill:none;}.cls-2{clip-path:url(#clip-path);}.cls-3{stroke:#2185c3;stroke-linecap:round;stroke-miterlimit:10;stroke-width:1.2px;}</style>
   <clipPath id="svg_5">
    <rect id="svg_7" height="30" width="30" class="cls-1"/>
   </clipPath>
  </defs>
  <g transform="rotate(-180 290,199.99999999999997) " id="svg_11">
   <g transform="matrix(19.75308830355425,0,0,19.75308830355425,0,0) " id="svg_8" class="cls-2">
    <line id="svg_9" y2="1.974999" x2="10.606249" y1="10.124999" x1="18.756249" class="cls-3"/>
    <line id="svg_10" y2="10.124999" x2="18.756249" y1="18.274999" x1="10.606249" class="cls-3"/>
   </g>
  </g>
 </g>
</svg>