import React from 'react';
import {connect} from 'react-redux';
import {TextBlock, MediaBlock, TextRow, RectShape, RoundShape} from 'react-placeholder/lib/placeholders';
import Immutable, {Map, List} from 'immutable';
import axios from 'axios';
import {some, range, keys} from 'lodash';
import Select from 'react-select';
import ContentPlaceholder from '../../components/contentPlaceholder';
import ProductItem from '../../components/productItem';
import Loader from '../../components/loader';
import ProgressButton from 'react-progress-button';
import {getQueryVariable, parseURLParams, slugify, toFormData} from '../../util/helpers';

class ProductExplorer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      explorer_categories: Map(),
      explorer_groups: Map(),
      explorer_views: Map(),
      products: List(),
      init_products: Map(),
      select: {
        category: 0,
        group: 0,
        view: 0
      },
      isVisible: false,
      imagesLoaded: false,
      loadCount: 0,
      buttonState: ''
    };
  }

  isEmpty(obj) {
    for(var key in obj) {
      if(obj.hasOwnProperty(key))
        return false;
    }
    return true;
  }

  componentWillReceiveProps(nextProps) {

    const products = this.props.products;
    if (nextProps.products.get('data') !== products.get('data') || products.get('explorer') !== nextProps.products.get('explorer')) {

      var urlString = window.location.href;
      var urlParams = parseURLParams(urlString);

      const category = (urlParams["category"] != null) ? urlParams["category"][0] : false;
      const group = (urlParams["group"] != null) ? urlParams["group"][0] : false;
      const view = (urlParams["view"] != null) ? urlParams["view"][0] : false;
      let categoryComp;

      switch(category){
        case 'preparation-products':
          categoryComp = "34";
          break;
        case 'interior-paints':
          categoryComp = "48";
          break;
        case 'exterior-paints':
          categoryComp = "63";
          break;
        case 'specialist-paints':
          categoryComp = "79";
          break;
        default:
          break;
      }

      if (categoryComp) {
        setTimeout(() => {
          this.onCategoryChange({value: categoryComp});
        });
      }

      if (group) {
        setTimeout(() => {
          this.onGroupChange({value: group});
        });
      }

      if (view) {
        setTimeout(() => {
          this.onViewClick({value: view});
        });
      }

      if (category == false) {
        setTimeout(() => {
          this.resetAllProducts(nextProps.products.get('data').toJS(), nextProps.products.get('explorer'));
          this.setState({explorer_views: nextProps.products.get('explorer'), select: Object.assign({}, this.state.select, {view: 0})})
        });
      }
    }
  }

  componentWillUpdate() {
    const galleryElement = this.refs.product_explorer;
    const imgElements = galleryElement.querySelectorAll('.image-wrapper img');
    if ((this.state.loadCount + 1) >= imgElements.length && !this.state.imagesLoaded) {
      this.setState({
        imagesLoaded: true
      })
      setTimeout(() => {
        if ($(".matchHeight").length) {
          $(".matchHeight").matchHeight();
        }
      });
    }
  }

  componentDidMount() {
    $(".main-search").on("click", () => {
      $(".js-search-app").slideDown(250).addClass('is-active');
    })

    setTimeout(() => {
      this.setState({ isLoading: false })
    }, 5000)
  }

  scrollToSection() {
    setTimeout(() => {
      $('html, body').animate({
        scrollTop: $(".explorer-view").offset().top
      }, 500);
    }, 0);
  }

  imagesLoaded(parentNode) {
    const imgElements = parentNode.querySelectorAll('.image-wrapper img');
    for (let i = 0, len = imgElements.length; i < len; i++) {
      if (imgElements[i].complete) {
        this.setState({
          loadCount: this.state.loadCount + 1
        });
      }
    }
  }

  imageOnLoadError() {
    this.setState({
      loadCount: this.state.loadCount + 1
    })
  }

  handleImageChange() {
    const galleryElement = this.refs.product_explorer;
    this.imagesLoaded(galleryElement)
  }

  resetAllProducts(products, explorer = Map()) {
    const results = {};
    results['All Products'] = {};
    results['All Products']['description'] = '';
    results['All Products']['data'] = products;
    this.setState({products: Immutable.fromJS(results), init_products: Immutable.fromJS(products)});
    setTimeout(() => {
      if (explorer.size) {
        this.updateProducts(explorer);
      }
      this.setVisibility(true);
    })
  }

  resetState() {
    this.setState({
      explorer_categories: Map(),
      explorer_groups: Map(),
      explorer_views: Map(),
      products: List(),
      select: {
        category: 0,
        group: 0,
        view: 0
      }
    });
  }

  setVisibility(isVisible) {
    this.setState({isVisible});
  }
  //Comment out method for creating product list since client didn't want it
  /* createProductListPDF() {

    this.setState({buttonState: 'creating'});
    const products = this.state.products;
    const products_explorer = this.props.products;
    const tax = products_explorer.get('explorer');
    const paints = (this.props.products.get('data') && this.props.products.get('data').size) ? this.props.products.get('data') : this.state.init_products;
    const categories = this.state.explorer_categories;
    const groups = this.state.explorer_groups;
    const views = this.state.explorer_views;
    const group_select = this.state.select.group;
    const view_select = this.state.select.view;
    const results = {};
    var data = {};
    let taxonomies = {};
    let flag = false;

    //If category has been selected, process otherwise include all categories
    if(categories.size == 1) {

      const category = categories.get('term_id');
      let name = '';

      tax.filter((taxonomy) => {
          if(category == taxonomy.term_id) {
            name = taxonomy.name;
          }
      })

      if(group_select != 0) {
        if(view_select != 0) {
          let view = {};

          if (view_select) {
            views.map((v) => {
              if (v.term_id === parseInt(view_select)) {
                view = v;
              }
            });
          }

          if (view.term_id) {
            taxonomies[view.term_id] = view;
            taxonomies = Map(taxonomies);
            flag = true;
          }
        } else {
            let group_view = Map(groups.get(group_select).children);
            if (group_view) {
              taxonomies = group_view;
              flag = true;
            }
        }
      } else {
        if(view_select != 0) {
          let view = {};

          if (view_select) {
            views.map((v) => {
              if (v.term_id === parseInt(view_select)) {
                view = v;
              }
            });
          }

          if (view.term_id) {
            taxonomies[view.term_id] = view;
            taxonomies = Map(taxonomies);
            flag = true;
          }
        }
      }

      if(flag) {
        paints
        .sortBy(product => product.menu_order)
        .map((product) => {
          return taxonomies
            .filter((taxonomy) => {
              if (!results[taxonomy.name]) {
                results[taxonomy.name] = {};
                results[taxonomy.name]['description'] = taxonomy.description;
                results[taxonomy.name]['data'] = [];
              }
              if (product['product-explorer'].indexOf(taxonomy.term_id) > -1) {
                if (!some(results[taxonomy.name]['data'], { 'id': product.id})) {
                  data = {}

                  if(product.better_featured_image.media_details.sizes.thumbnail.source_url) {
                    data.image = product.better_featured_image.media_details.sizes.thumbnail.source_url;
                  } else {
                    data.image = '';
                  }

                  data.title = product.title.rendered;
                  data.description = product.acf.product_intro_description;

                  if(product.acf.product_downloads[0] != undefined) {
                    data.link = product.acf.product_downloads[0].product_download_item_file.url;
                  } else {
                    data.link = '#';
                  }

                  data = JSON.stringify(data);
                  data = JSON.parse(data);

                  results[taxonomy.name]['data'].push(data);
                }
              }
            })
        });
      } else {
        paints
          .sortBy(product => product.menu_order)
          .filter((product) => {
              if(product['pure_taxonomies']['product-explorer']) {
                  product['pure_taxonomies']['product-explorer'].filter((test) => {
                  if(test['parent'] == category) {
                    if (!results[name]) {
                      results[name] = {};
                      results[name]['description'] = '';
                      results[name]['data'] = [];
                    }

                    if (!some(results[name]['data'], { 'id': product.id})) {
                      data = {}
                      if(product.better_featured_image.media_details.sizes.thumbnail.source_url) {
                        data.image = product.better_featured_image.media_details.sizes.thumbnail.source_url;
                      } else {
                        data.image = '';
                      }

                      data.title = product.title.rendered;
                      data.description = product.acf.product_intro_description;
                      if(product.acf.product_downloads[0] != undefined) {
                        data.link = product.acf.product_downloads[0].product_download_item_file.url;
                      } else {
                        data.link = '#';
                      }

                      data = JSON.stringify(data);
                      data = JSON.parse(data);
                      results[name]['data'].push(data);
                    }
                  }
                })
              }
          });
      }
    } else {
      //Process all groups and categories
      paints
        .sortBy(product => product.menu_order)
        .map((product) => {
            return tax
              .filter((taxonomy) => {
                if (!results[taxonomy.name]) {
                  results[taxonomy.name] = {};
                  results[taxonomy.name]['description'] = taxonomy.description;
                  results[taxonomy.name]['data'] = [];
                }
                if (product['product-explorer'].indexOf(taxonomy.term_id) > -1) {
                  if (!some(results[taxonomy.name]['data'], { 'id': product.id})) {
                    data = {}

                    if(product.better_featured_image.media_details.sizes.thumbnail.source_url) {
                      data.image = product.better_featured_image.media_details.sizes.thumbnail.source_url;
                    } else {
                      data.image = '';
                    }

                    data.title = product.title.rendered;
                    data.description = product.acf.product_intro_description;

                    if(product.acf.product_downloads[0] != undefined) {
                      data.link = product.acf.product_downloads[0].product_download_item_file.url;
                    } else {
                      data.link = '#';
                    }

                    data = JSON.stringify(data);
                    data = JSON.parse(data);

                    results[taxonomy.name]['data'].push(data);
                  }
                }
              })
          });
    }


    //Append nested objects and arrays using toFormData helper utility
    var formData = toFormData(results);
    var data_array = [];

    if (this.state && products.size) {
      formData.append('action', 'product_pdf_print');

      const that = this;
      axios({
        method: 'post',
        url: '/wp-admin/admin-ajax.php',
        data: formData,
        responseType:'blob',
        processData:false,
        contentType:false
      })
      .then(function (response) {
        var filename = 'Duram Smart Paint Products List.pdf';
        var mime = 'application/pdf';

        var blob = new Blob([response.data], {type: mime || 'application/octet-stream'});
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
            // IE workaround for "HTML7007: One or more blob URLs were
            // revoked by closing the blob for which they were created.
            // These URLs will no longer resolve as the data backing
            // the URL has been freed."
            window.navigator.msSaveBlob(blob, filename);
        }
        else {
            var blobURL = window.URL.createObjectURL(blob);
            var tempLink = document.createElement('a');
            tempLink.style.display = 'none';
            tempLink.href = blobURL;
            tempLink.setAttribute('download', filename);

            // Safari thinks _blank anchor are pop ups. We only want to set _blank
            // target if the browser does not support the HTML5 download attribute.
            // This allows you to download files in desktop safari if pop up blocking
            // is enabled.
            if (typeof tempLink.download === 'undefined') {
                tempLink.setAttribute('target', '_blank');
            }

            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            window.URL.revokeObjectURL(blobURL);
        }
        that.setState({buttonState: 'success'});
      })
      .catch(function (error) {
        console.log(error.response);
        if(error.response) {
          that.setState({buttonState: 'error'});
        }
      });
    }
  } */

  onCategoryChange(category) {
    this.setVisibility(false);
    this.resetState();
    this.setState({
      explorer_categories: category.value.length ? Map({term_id: category.value}) : Map(),
      select: Object.assign({}, this.state.select, {category: category.value, group: 0, view: 0})
    });
    const products = this.props.products;
    const explorer = products.get('explorer');
    if (explorer.size && category.value && explorer.get(category.value)) {
      let groups = Map(explorer.get(category.value).children);
      if (groups) {
        this.setState({
          explorer_groups: groups,
          explorer_views: groups
        });
        this.updateCategoryProducts(category.value);
      }
    } else {
      this.setState({
        select: Object.assign({}, this.state.select, {category: 0, group: 0, view: 0}),
        explorer_views: products.get('explorer')
      });
      this.resetAllProducts(products.get('data').toJS(), products.get('explorer'));
    }
  }

  onGroupChange(group) {
    const groups = this.state.explorer_groups;

    if (!groups.size) {
      this.onCategoryChange(group);
      return false
    }
    this.setVisibility(false);
    this.setState({
      explorer_views: Map(),
      select: Object.assign({}, this.state.select, {group: group.value, view: 0})
    });
    if (groups.size && group.value) {
      let views = Map(groups.get(group.value).children);
      if (views) {
        this.setState({
          explorer_views: views
        });
        this.updateProducts(views);
      }
    } else {
      let category = this.state.select.category;
      this.updateCategoryProducts(category);
      this.setState({explorer_views: groups, select: Object.assign({}, this.state.select, {group: 0, view: 0})})
    }
    this.scrollToSection();
  }

  onViewClick(view) {
    const children = this.isEmpty(view.children);

    if(!children) {
      let group = {};
      group.value = view.term_id.toString();
      group.label = view.name;
      this.onGroupChange(group);
      return false;
    }

    let term = {};
    if (view.value) {
      this.state.explorer_views.map((v) => {
        if (v.term_id === parseInt(view.value)) {
          view = v;
        }
      });
    }

    if (view.term_id) {
      term[view.term_id] = view;
      term = Map(term);
      this.setState({select: Object.assign({}, this.state.select, {view: view.term_id})})
    } else {
      this.setState({select: Object.assign({}, this.state.select, {view: 0})})
      term = this.state.explorer_views;
    }

    this.updateProducts(term);
    this.scrollToSection();
  }

  updateProducts(items) {
    if (items.size) {
      const products = (this.props.products.get('data') && this.props.products.get('data').size) ? this.props.products.get('data') : this.state.init_products;
      const taxonomies = items;
      const results = {};
      products
        .sortBy(product => product.menu_order)
        .map((product) => {
        return taxonomies
          .filter((taxonomy) => {
            // Add safety checks for taxonomy properties
            if (!taxonomy || !taxonomy.name || !taxonomy.term_id) {
              return false;
            }
            if (!results[taxonomy.name]) {
              results[taxonomy.name] = {};
              results[taxonomy.name]['description'] = taxonomy.description || '';
              results[taxonomy.name]['data'] = [];
            }
            if (product['product-explorer'] && product['product-explorer'].indexOf(taxonomy.term_id) > -1) {
              if (!some(results[taxonomy.name]['data'], { 'id': product.id})) {
                results[taxonomy.name]['data'].push(product);
              }
            }
          })
      });

      // Check if taxonomies has the expected structure before sorting
      if (taxonomies && taxonomies.sortBy && typeof taxonomies.sortBy === 'function') {
        const orderResults = {};
        taxonomies
          .sortBy(taxonomy => (taxonomy && taxonomy.menu_order) ? taxonomy.menu_order : 0)
          .forEach(taxonomy => {
            if (taxonomy && taxonomy.name && results[taxonomy.name]) {
              orderResults[taxonomy.name] = results[taxonomy.name];
            }
          });
        this.setState({products: Immutable.fromJS(orderResults)});
      } else {
        // Fallback to original behavior if sorting fails
        this.setState({products: Immutable.fromJS(results)});
      }
    } else {
      console.log("NO ITEMS")
    }
    setTimeout(() => {
      this.setVisibility(true);
    })
  }

  updateCategoryProducts(items) {
    //console.log(items);
    if (items) {
      const products = (this.props.products.get('data') && this.props.products.get('data').size) ? this.props.products.get('data') : this.state.init_products;
      const category = items;
      const results = {};

      products
        .sortBy(product => product.menu_order)
        .filter((product) => {
          if(product['pure_taxonomies']['product-explorer']) {
              product['pure_taxonomies']['product-explorer'].filter((test) => {
              if(test['parent'] == category) {
                if (!results['All']) {
                  results['All'] = {};
                  results['All']['description'] = '';
                  results['All']['data'] = [];
                }

                if (!some(results['All']['data'], { 'id': product.id})) {
                  results['All']['data'].push(product);
                }
              }
            })
          }
      });
      // For updateCategoryProducts, we don't need to sort by taxonomy order
      // since it only creates one "All" category
      this.setState({products: Immutable.fromJS(results)});
    } else {
      console.log("NO ITEMS")
    }
    setTimeout(() => {
      this.setVisibility(true);
    })
  }

  renderCategorySelect() {
    const products = this.props.products;
    const explorer = products.get('explorer');
    let categories = [{value: 0, label: 'All Products'}];
    if (explorer.size) {
      explorer
        .map((category) => {
        if (category && category.term_id && category.name) {
          categories.push({value: category.term_id.toString(), label: category.name});
        }
      });
    }
    return (
      <Select
        name="explorer_category"
        value={this.state.select.category}
        options={categories}
        clearable={false}
        searchable={false}
        onChange={this.onCategoryChange.bind(this)}
      />
    );
  }

  renderGroupSelect() {
    let groups = [{value: 0, label: 'All Groups'}];
    if (this.state.explorer_groups.size) {
      this.state.explorer_groups
        //.sortBy(group => group.term_order)
        .map((group) => {
        if (group && group.term_id && group.name) {
          groups.push({value: group.term_id.toString(), label: group.name});
        }
      });
    }
    return (
      <Select
        name="explorer_group"
        options={groups}
        value={this.state.select.group}
        clearable={false}
        searchable={false}
        onChange={this.onGroupChange.bind(this)}
        disabled={!this.state.explorer_groups.size || false}
      />
    );
  }

  renderViewSelect() {
    let views = [{value: 0, label: 'All Views'}];
    if (this.state.explorer_views.size) {
      this.state.explorer_views
        //.sortBy(view => view.term_order)
        .map((view) => {
        if (view && view.term_id && view.name) {
          views.push({value: view.term_id, label: view.name});
        }
      });
    }
    return (
      <Select
        name="explorer_view"
        options={views}
        value={this.state.select.view}
        clearable={false}
        searchable={false}
        onChange={this.onViewClick.bind(this)}
        disabled={!this.state.explorer_views.size || false}
      />
    );
  }

  renderViews() {
    const views = this.state.explorer_views
      .map((view) => {
        if (view && view.term_id && view.name) {
          const viewClass = (view.term_id === this.state.select.view) ? 'view-selected' : ''
          return (
            <div className={`col-lg-auto col-md-auto col-sm-auto col-sm-6  col-6`}>
              <a className={viewClass} dangerouslySetInnerHTML={{__html:view.name}} onClick={() => this.onViewClick(view)} href='javascript:void(0)' />
            </div>
          );
        }
        return null;
      })
      .filter(view => view !== null);
    const allClass = this.state.select.view === 0 ? 'view-selected' : '';
    return (
      <div className="explorer-view">
        <div className="container no-padding">
          <div className="row">
            <div className="col-lg-auto col-md-auto col-sm-auto col-12">
              VIEW:
            </div>
            <div className="col-lg-auto col-md-auto col-sm-auto col-sm-6  col-6">
              <a className={allClass} href='javascript:void(0)' onClick={() => this.onViewClick({})} >All</a>
            </div>
            {views}
          </div>
        </div>
      </div>
    );
  }

  renderProductImage(item) {
    return item.getIn(['better_featured_image']) || Map();
  }

  renderEmptyState() {
    return (
      <div className='my-awesome-placeholder'>
        <RectShape color='blue' style={{width: 30, height: 80}}/>
        <TextBlock rows={7} color='yellow'/>
      </div>
    );
  }

  renderIcons(item) {
    let icons = Map({
      left: null,
      leftAlt: null,
      right: null,
      rightAlt: null
    });
    const productGuaranteeIcon = item.getIn(['pure_taxonomies', 'product-guarantee']);
    if (productGuaranteeIcon) {
      if (productGuaranteeIcon.size && productGuaranteeIcon.first().get('icon')){
        icons = icons
          .set('right', productGuaranteeIcon.first().get('icon'))
          .set('rightAlt', productGuaranteeIcon.first().get('name'));

      }
    }
    const productExclusiveTo = item.getIn(['pure_taxonomies', 'product-exclusive-to']);
    if (productExclusiveTo) {
      if (productExclusiveTo.size && productExclusiveTo.first().get('icon')){
        icons = icons
          .set('left', productExclusiveTo.first().get('icon'))
          .set('leftAlt', productExclusiveTo.first().get('name'));
      }
    }
    return icons;
  }

  renderProductItems(items) {
    if (items.size) {
      const products = items.map((item, index) => {
        return (
          <div
            className="product-list-item col-lg-3 col-md-4 col-6 col-sm-6 half-gutters"
            key={`item-${item.get('id')}-${index}`}
          >
            <ProductItem
              title={item.getIn(['title', 'rendered'])}
              image={this.renderProductImage(item)}
              descriptor={item.getIn(['acf', 'product_descriptor'])}
              link={item.get('link')}
              overlay={{
                title: item.getIn(['title', 'rendered']),
                description: item.getIn(['acf', 'product_intro_description']),
                link: item.get('link'),
                linkText: 'View Product'
              }}
              onImageLoad={this.handleImageChange.bind(this)}
              onImageError={this.imageOnLoadError.bind(this)}
              icons={this.renderIcons(item)}
            />
          </div>
        );
      });
      return (
        <div className="row half-gutter-row">
          {products}
        </div>
      );
    }
    return null;
  }

  renderTaxonomyDescription(description) {
    if (description && description.trim().length > 0) {
      return (
        <div className="col-md-8 offset-md-2 tax-desc t-text-center">
          {description}
        </div>
      );
    }
    return null;
  }

  renderProductResults() {
    const products = this.state.products;
    if (this.state && products.size) {
      const items = products.map((product, heading) => {
        return (
          <div className="row">
            <div className="col-sm-12">
              <h1
                className="explorer-section-title"
                dangerouslySetInnerHTML={{__html:heading}}
                id={slugify(heading)}
              />
            </div>
            {this.renderTaxonomyDescription(product.get('description'))}
            <div className="col-sm-12">
              {this.renderProductItems(product.get('data'))}
            </div>
          </div>
        );
      });
      return items;
    } else if (!this.props.products.get('data').size) {

      const numContainers = range(12);
      const emptyState = numContainers.map((container, index) => {
        return (
          <div className="col-sm-3 half-gutters" key={`${index}.col`}>
            <ContentPlaceholder
              ready={this.state.isVisible}
              type='media'
              showLoadingAnimation
              customPlaceholder={this.renderEmptyState()}
              style={{padding: '0 0 20px 0'}}
            />
          </div>
        )
      })

      return (
        <div>
          <p></p>
        </div>
      );
    }
    return null;
  }

  renderFloatingExplorer() {
    return (
      <div className="product-explorer-floating-wrapper">
        <div className="product-explorer-floating-inner select-control">
          <div className="pe-container">
            <div className="o-header js-header">
              <div className="o-header__inner">
                <div className="o-header__logo ">
                  <a href="/">
                    <img src="/wp-content/themes/duram/resources/assets/images/logo.svg" alt="Duram Paint" />
                  </a>
                </div>

                <div className="o-header-mobile-wrapper">
                  <a href="#" className="c-mobile-menu-toggle js-mobile-menu-toggle">
                    <div className="c-mobile-menu-toggle__inner">
                      <span></span>
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </a>
                  <div className="c-mobile-search">
                    <a href="#" className="c-mobile-search__link js-mobile-search-toggle"></a>
                    <form method="get" action="">
                      <input type="text" className="c-mobile-search__input"/>
                    </form>
                  </div>
                </div>

                <div className="o-header-desktop-wrapper">

                    <div className="row c-product-explorer-filter-row">
                      <div className="col-sm-12">
                        <div className="row product-explorer-floating-wrapper__row">
                          <div className="col-sm-3 product-explorer-floating-wrapper__col">
                            {this.renderCategorySelect()}
                          </div>
                          <div className="col-sm-3 product-explorer-floating-wrapper__col">
                            {this.renderGroupSelect()}
                          </div>
                          <div className="col-sm-3 product-explorer-floating-wrapper__col">
                            {this.renderViewSelect()}
                          </div>
                          <div className="col-sm-3 product-explorer-floating-wrapper__small-col">
                            <div className="o-desktop-menu-buttons">
                              <a href="/find-the-right-product/" className="c-desktop-menu-button">Find Products</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>


                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
    );
  }

  render() {
    return (
      <div className="row">
        <div className="col-sm-12">
          {this.renderFloatingExplorer()}
          <div className="product-explorer-controls">
            <div className="container no-padding">
              <div className="row justify-content-md-center select-control">
                <div className="col-md-3 label">
                  <table height="100%">
                    <tbody>
                      <tr>
                        <td className="align-middle c-text-white">PAINT CATEGORIES:</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="col-md-9">
                  {this.renderCategorySelect()}
                </div>
              </div>
              <div className="row justify-content-md-center select-control">
                <div className="col-md-3 label">
                  <table height="100%">
                    <tbody>
                      <tr>
                        <td className="align-middle c-text-white">GROUP PRODUCTS BY:</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="col-md-9">
                  {this.renderGroupSelect()}
                </div>
              </div>
            </div>
          </div>
          {this.renderViews()}
        </div>
        <div className="col-sm-12">
          <div className="container no-padding" ref="product_explorer">
            {this.renderProductResults()}
          </div>
        </div>
        {/* <div id="mybuttonwrapper">
          { this.state.buttonState }
          <div id="mybutton">
            <ProgressButton onClick={() => this.createProductListPDF()} state={this.state.buttonState}>
              Download Product List <img id="button-icon" src="/wp-content/themes/duram/dist/images/download.svg" alt="Duram Paint" />
            </ProgressButton>
          </div>
        </div> */}
      </div>
    );
  }
}

export default connect(state => ({
  products: state.products || List(),
  productData: state.products.get('data') || List(),
}))(ProductExplorer);
